/**
 * AWS Lambda handler for processing salary revision import operations via Step Function
 * This function is invoked by AWS Step Functions to process individual salary revision records
 * ONLY supports formId 360 (Salary Revision) with new simplified 6-field input structure
 */

const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { calculateSalaryRevision } = require('./salaryCalculation');
const { validateSalaryRevision } = require('./salaryValidation');
const { persistSalaryRevision } = require('./salaryPersistence');

/**
 * AWS Lambda handler function
 * @param {Object} event - Step Function event containing salary revision record data
 * @param {Object} context - Lambda context object
 * @returns {Object} - Processing result
 */
exports.handler = async (event, context) => {
  let organizationDbConnection;
  
  try {
    console.log('=== PROCESSING SALARY REVISION IMPORT RECORD ===');
    console.log('Event:', JSON.stringify(event, null, 2));

    // Extract data from Step Function event
    const {
      salaryImportId,
      salaryRevisionRecord,
      orgCode,
      loginEmployeeId
    } = event;

    // Validate required parameters
    if (!salaryImportId || !salaryRevisionRecord || !orgCode || !loginEmployeeId) {
      throw new Error('Missing required parameters: salaryImportId, salaryRevisionRecord, orgCode, loginEmployeeId');
    }

    // Initialize database connection
    const dbConfig = await commonLib.func.getDbConnection(orgCode);
    organizationDbConnection = knex(dbConfig);

    console.log(`Processing salary revision for Employee ID: ${salaryRevisionRecord.Employee_Id}, Import: ${salaryImportId}`);

    // Phase 1: Calculate salary revision structure
    console.log('=== PHASE 1: SALARY REVISION CALCULATION ===');
    const calculationResult = await calculateSalaryRevision(
      organizationDbConnection,
      salaryRevisionRecord,
      loginEmployeeId,
      orgCode
    );

    if (calculationResult.errorCode) {
      console.error('Salary revision calculation failed:', calculationResult.message);
      return {
        success: false,
        phase: 'CALCULATION',
        errorCode: calculationResult.errorCode,
        errorMessage: calculationResult.message,
        employeeId: salaryRevisionRecord.Employee_Id,
        salaryImportId: salaryImportId
      };
    }

    console.log('Salary revision calculation completed successfully');

    // Phase 2: Validate calculated revision data
    console.log('=== PHASE 2: SALARY REVISION VALIDATION ===');
    const validationResult = await validateSalaryRevision(
      organizationDbConnection,
      calculationResult.calculatedData,
      loginEmployeeId,
      orgCode
    );

    if (validationResult.errorCode) {
      console.error('Salary revision validation failed:', validationResult.message);
      return {
        success: false,
        phase: 'VALIDATION',
        errorCode: validationResult.errorCode,
        errorMessage: validationResult.message,
        employeeId: salaryRevisionRecord.Employee_Id,
        salaryImportId: salaryImportId
      };
    }

    console.log('Salary revision validation completed successfully');

    // Phase 3: Persist salary revision to database
    console.log('=== PHASE 3: SALARY REVISION PERSISTENCE ===');
    const persistenceResult = await persistSalaryRevision(
      organizationDbConnection,
      validationResult.validatedData,
      loginEmployeeId,
      orgCode
    );

    if (persistenceResult.errorCode) {
      console.error('Salary revision persistence failed:', persistenceResult.message);
      return {
        success: false,
        phase: 'PERSISTENCE',
        errorCode: persistenceResult.errorCode,
        errorMessage: persistenceResult.message,
        employeeId: salaryRevisionRecord.Employee_Id,
        salaryImportId: salaryImportId
      };
    }

    console.log('Salary revision persistence completed successfully');

    // Return success result
    return {
      success: true,
      phase: 'COMPLETE',
      employeeId: salaryRevisionRecord.Employee_Id,
      salaryImportId: salaryImportId,
      message: 'Salary revision record processed successfully',
      persistedIds: persistenceResult.persistedIds
    };

  } catch (error) {
    console.error('Error in processSalaryRevisionImportFunction:', error);
    
    return {
      success: false,
      phase: 'ERROR',
      errorCode: 'PROCESSING_ERROR',
      errorMessage: error.message || 'An unexpected error occurred during salary revision processing',
      employeeId: event.salaryRevisionRecord?.Employee_Id || 'Unknown',
      salaryImportId: event.salaryImportId || 'Unknown'
    };
    
  } finally {
    // Clean up database connections
    if (organizationDbConnection) {
      try {
        organizationDbConnection.destroy();
      } catch (destroyError) {
        console.warn('Warning: Error destroying database connection:', destroyError.message);
      }
    }
  }
};
