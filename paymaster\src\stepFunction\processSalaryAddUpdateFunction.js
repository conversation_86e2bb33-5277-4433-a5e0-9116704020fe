/**
 * Master Salary Add/Update Orchestrator
 * 
 * Orchestrates the 3-phase salary processing flow:
 * Phase 1: Calculate - Pure calculation via existing resolver
 * Phase 2: Validate - Business rules validation
 * Phase 3: Persist - Atomic database write OR capture error
 * 
 * This function is invoked by Step Function Map state for each employee
 * in the batch, with a maximum concurrency of 5 employees at a time.
 */

const { calculateEmployeeSalary } = require('./salaryCalculation');
const { validateCalculatedSalary } = require('./salaryValidation');
const { persistSalaryData, captureProcessingError } = require('./salaryPersistence');

/**
 * Process a single employee salary add/update through all 3 phases
 * 
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {string} importId - Batch import ID (from salary_import_tracking)
 * @param {number} employeeId - Employee being processed
 * @param {Object} salaryInput - Salary parameters {annualCtc, formId, salaryEffectiveMonth, createdBy}
 * @returns {Promise<Object>} Result with success, phase, salaryId, error details
 */
const processSalaryAddUpdateFunction = async (organizationDbConnection, importId, employeeId, salaryInput) => {
  const startTime = Date.now();
  
  console.log('╔════════════════════════════════════════════════════════════════════════════╗');
  console.log('║ SALARY IMPORT ORCHESTRATOR - PROCESSING EMPLOYEE                          ║');
  console.log('╚════════════════════════════════════════════════════════════════════════════╝');
  console.log(`Import ID: ${importId}`);
  console.log(`Employee ID: ${employeeId}`);
  console.log(`Annual CTC: ${salaryInput.annualCtc}`);
  console.log(`Form ID: ${salaryInput.formId}`);
  console.log(`Salary Effective Month: ${salaryInput.salaryEffectiveMonth || 'Not specified'}`);
  console.log('────────────────────────────────────────────────────────────────────────────');

  // Add startTime to salaryInput for duration tracking in Phase 3
  salaryInput.startTime = startTime;

  try {
    // ═══════════════════════════════════════════════════════════════════════════════════
    // PHASE 1: CALCULATE SALARY
    // ═══════════════════════════════════════════════════════════════════════════════════
    console.log('\n[1/3] PHASE 1: CALCULATE SALARY');
    console.log('─────────────────────────────────────────────────────────────────────────');

    const calculationResponse = await calculateEmployeeSalary(organizationDbConnection, salaryInput);

    if (!calculationResponse.success) {
      console.error(`❌ Calculation failed: ${calculationResponse.errorCode} - ${calculationResponse.errorMessage}`);
      
      // Phase 1 failed → go to Phase 3B
      await captureProcessingError(
        organizationDbConnection,
        importId,
        employeeId,
        salaryInput,
        calculationResponse.errorCode,
        calculationResponse.errorMessage,
        'CALCULATE'
      );

      const duration = Date.now() - startTime;

      return {
        success: false,
        employeeId: employeeId,
        phase: 'CALCULATE',
        errorCode: calculationResponse.errorCode,
        errorMessage: calculationResponse.errorMessage,
        salaryId: null,
        processingDuration: duration,
        validationErrors: null
      };
    }

    console.log('✅ Calculation successful');
    console.log(`   - Salary Structure: ${Object.keys(calculationResponse.salaryStructure).length} components`);
    console.log(`   - Employee Retiral Details: Available`);

    // ═══════════════════════════════════════════════════════════════════════════════════
    // PHASE 2: VALIDATE CALCULATED SALARY
    // ═══════════════════════════════════════════════════════════════════════════════════
    console.log('\n[2/3] PHASE 2: VALIDATE CALCULATED SALARY');
    console.log('─────────────────────────────────────────────────────────────────────────');

    const validationResult = await validateCalculatedSalary(calculationResponse, salaryInput);

    if (!validationResult.valid) {
      console.error(`❌ Validation failed with ${validationResult.errors.length} errors:`);
      validationResult.errors.forEach(err => {
        console.error(`   - [${err.code}] ${err.message}`);
      });

      // Phase 2 failed → go to Phase 3B with validation errors
      await captureProcessingError(
        organizationDbConnection,
        importId,
        employeeId,
        salaryInput,
        'VALIDATION_FAILED',
        'Salary validation failed - check Validation_Errors field',
        'VALIDATE',
        validationResult.errors
      );

      const duration = Date.now() - startTime;

      return {
        success: false,
        employeeId: employeeId,
        phase: 'VALIDATE',
        errorCode: 'VALIDATION_FAILED',
        errorMessage: 'Salary validation failed - check validation_errors field',
        salaryId: null,
        processingDuration: duration,
        validationErrors: validationResult.errors
      };
    }

    console.log('✅ Validation successful');
    console.log('   - All business rules passed');

    // ═══════════════════════════════════════════════════════════════════════════════════
    // PHASE 3: PERSIST SALARY DATA TO DATABASE
    // ═══════════════════════════════════════════════════════════════════════════════════
    console.log('\n[3/3] PHASE 3: PERSIST SALARY DATA');
    console.log('─────────────────────────────────────────────────────────────────────────');

    const persistenceResult = await persistSalaryData(
      organizationDbConnection,
      calculationResponse,
      salaryInput,
      importId,
      employeeId
    );

    if (!persistenceResult.success) {
      console.error(`❌ Persistence failed: ${persistenceResult.errorCode}`);

      // Phase 3A failed → go to Phase 3B (but transaction already rolled back)
      await captureProcessingError(
        organizationDbConnection,
        importId,
        employeeId,
        salaryInput,
        persistenceResult.errorCode,
        persistenceResult.errorMessage,
        'PERSIST'
      );

      const duration = Date.now() - startTime;

      return {
        success: false,
        employeeId: employeeId,
        phase: 'PERSIST',
        errorCode: persistenceResult.errorCode,
        errorMessage: persistenceResult.errorMessage,
        salaryId: null,
        processingDuration: duration,
        validationErrors: null
      };
    }

    // ═══════════════════════════════════════════════════════════════════════════════════
    // SUCCESS: ALL PHASES COMPLETED
    // ═══════════════════════════════════════════════════════════════════════════════════
    console.log('✅ Persistence successful');
    console.log(`   - Salary ID: ${persistenceResult.salaryId}`);

    const duration = Date.now() - startTime;

    console.log('\n╔════════════════════════════════════════════════════════════════════════════╗');
    console.log('║ ✅ PROCESSING COMPLETE - EMPLOYEE SALARY ADDED                           ║');
    console.log('╚════════════════════════════════════════════════════════════════════════════╝');
    console.log(`Duration: ${duration}ms`);
    console.log(`Salary ID: ${persistenceResult.salaryId}`);

    return {
      success: true,
      employeeId: employeeId,
      phase: 'COMPLETE',
      errorCode: null,
      errorMessage: null,
      salaryId: persistenceResult.salaryId,
      processingDuration: duration,
      validationErrors: null
    };

  } catch (error) {
    // Unexpected error - shouldn't reach here but handle gracefully
    console.error('╔════════════════════════════════════════════════════════════════════════════╗');
    console.error('║ ❌ UNEXPECTED ERROR IN ORCHESTRATOR                                      ║');
    console.error('╚════════════════════════════════════════════════════════════════════════════╝');
    console.error('Error:', error);

    const duration = Date.now() - startTime;

    // Try to capture this unexpected error
    try {
      await captureProcessingError(
        organizationDbConnection,
        importId,
        employeeId,
        salaryInput,
        'ORCHESTRATOR_ERROR',
        error.message,
        'ORCHESTRATOR'
      );
    } catch (captureError) {
      console.error('Failed to capture orchestrator error:', captureError);
    }

    return {
      success: false,
      employeeId: employeeId,
      phase: 'ORCHESTRATOR',
      errorCode: 'ORCHESTRATOR_ERROR',
      errorMessage: error.message || 'Unexpected error in salary processing orchestrator',
      salaryId: null,
      processingDuration: duration,
      validationErrors: null
    };
  }
};

// Export for both Step Function (handler) and module usage
module.exports = {
  processSalaryAddUpdateFunction: processSalaryAddUpdateFunction
};
