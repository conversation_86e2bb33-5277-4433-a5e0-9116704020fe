// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../common/tablealias');

/**
 * Get default payout month for one-time earning based on type configuration
 * @param {Object} parent - GraphQL parent object
 * @param {Object} args - Arguments containing formId, employeeId, oneTimeEarningTypeId
 * @param {Object} context - Context object with connection info
 * @param {Object} info - GraphQL info object
 * @returns {Object} - Response with default payout month
 */
const getDefaultPayoutMonth = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};

    try {
        console.log('Inside getDefaultPayoutMonth function');

        const loggedInEmpId = context.logInEmpId;

        // Get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loggedInEmpId,
            null,
            '',
            'UI',
            false,
            args.formId
        );

        if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            throw '_DB0100'; // View access denied
        }

        // Get one-time earning type configuration
        const typeConfig = await organizationDbConnection(ehrTables.onetimeEarningTypes)
            .select(
                'Default_Payout_Month_From',
                'Default_Payout_Duration_Months',
                'Commitment_Period',
                'Commitment_Period_Months'
            )
            .where('One_Time_Earning_Type_Id', args.oneTimeEarningTypeId)
            .first();

        if (!typeConfig) {
            throw 'PST0905'; // One-time earning type not found
        }

        let defaultPayoutMonth = null;
        let commitmentStartMonth = null;
        let commitmentEndMonth = null;

        // Fetch employee job data once (Date_Of_Join)
        const empJob = await organizationDbConnection(ehrTables.empJob)
            .select('Date_Of_Join')
            .where('Employee_Id', args.employeeId)
            .first();

        // Get employee salary type for maxPayslipMonth calculation
        const employeeSalaryType = await commonLib.payroll.getEmployeeSalaryType(
            organizationDbConnection,
            args.employeeId
        );

        // Determine default payout month based on Default_Payout_Month_From setting
        const defaultPayoutMonthFrom = (typeConfig.Default_Payout_Month_From || '').toLowerCase();

        if (defaultPayoutMonthFrom === 'date of joining') {
            if (!empJob || !empJob.Date_Of_Join) {
                throw 'PST0906'; // Employee date of join not found
            }

            // Convert Date_Of_Join to M,YYYY format
            const dateOfJoin = moment(empJob.Date_Of_Join);
            if (!dateOfJoin.isValid()) {
                throw 'PST0907'; // Invalid date of join
            }
            let payoutMoment;
            if (typeConfig.Default_Payout_Duration_Months) {
                 payoutMoment = dateOfJoin.clone().add(typeConfig.Default_Payout_Duration_Months, 'months');
            }
            else{
                payoutMoment = dateOfJoin;
            }
            defaultPayoutMonth = payoutMoment.format('M,YYYY');

        } else if (defaultPayoutMonthFrom === 'current payout month') {
            // Get max payslip month and add 1 month
            const employeeMaxPayslipMonth = await commonLib.func.maxPayslipMonth(
                organizationDbConnection,
                args.employeeId,
                employeeSalaryType
            );

            if (!employeeMaxPayslipMonth) {
                if (!empJob || !empJob.Date_Of_Join) {
                    throw 'PST0906'; // Employee date of join not found
                }
                const dateOfJoin = moment(empJob.Date_Of_Join);
                if (!dateOfJoin.isValid()) {
                    throw 'PST0907'; // Invalid date of join
                }
                let payoutMoment = dateOfJoin;
                defaultPayoutMonth = payoutMoment.format('M,YYYY');
            }
            else {

            // Convert YYYY-MM-00 format to M,YYYY format and add 1 month
            const [year, month] = employeeMaxPayslipMonth.split('-');
            const maxPayslipMoment = moment(`${year}-${month}`, 'YYYY-MM');
            if (!maxPayslipMoment.isValid()) {
                // Fallback to Date_Of_Join - perform same validation as DOJ branch
                if (!empJob || !empJob.Date_Of_Join) {
                    throw 'PST0906'; // Employee date of join not found
                }

                const dateOfJoin = moment(empJob.Date_Of_Join);
                if (!dateOfJoin.isValid()) {
                    throw 'PST0907'; // Invalid date of join
                }
                defaultPayoutMonth = dateOfJoin.format('M,YYYY');
                
            }
            else {

            // Add 1 month to get next payout month
                const nextPayoutMonth = maxPayslipMoment.clone().add(1, 'month');
                let payoutMoment;
                if (typeConfig.Default_Payout_Duration_Months) {
                    payoutMoment = nextPayoutMonth.clone().add(typeConfig.Default_Payout_Duration_Months, 'months');
                    defaultPayoutMonth = payoutMoment.format('M,YYYY');
                }
                else{
                    payoutMoment = nextPayoutMonth;
                    defaultPayoutMonth = payoutMoment.format('M,YYYY');
                }

            }
        }

        } else {
            // Invalid or null Default_Payout_Month_From setting
            throw 'PST0912'; // Invalid default payout month configuration
        }

        // Calculate commitment start and end months if commitment period is enabled
        // Commitment period uses the same reference as payout month (defaultPayoutMonth)
        if (typeConfig.Commitment_Period === 'Yes' && typeConfig.Commitment_Period_Months && defaultPayoutMonth) {
            // Parse the default payout month (M,YYYY format) to use as commitment start
            const [month, year] = defaultPayoutMonth.split(',');
            const commitmentStartMoment = moment(`${year}-${month}`, 'YYYY-M');

            if (!commitmentStartMoment.isValid()) {
                throw 'PST0912'; // Invalid default payout month configuration
            }

            // Commitment starts from the payout month
            commitmentStartMonth = commitmentStartMoment.format('M,YYYY');

            // Calculate commitment end month (start month + commitment period months)
            const commitmentEndMoment = commitmentStartMoment.clone().add(typeConfig.Commitment_Period_Months, 'months');
            commitmentEndMonth = commitmentEndMoment.format('M,YYYY');
        }

        return {
            success: true,
            message: 'Default payout month retrieved successfully.',
            errorCode: null,
            defaultPayoutMonth: defaultPayoutMonth,
            commitmentStartMonth: commitmentStartMonth,
            commitmentEndMonth: commitmentEndMonth
        };

    } catch (mainCatchError) {
        console.log('Error in getDefaultPayoutMonth function main catch block', mainCatchError);
        if (mainCatchError === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            const errResult = commonLib.func.getError(mainCatchError, 'PST0913');
            throw new ApolloError(errResult.message, errResult.code);
        }
    } finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};

// resolver definition
const resolvers = {
    Query: {
        getDefaultPayoutMonth
    }
};

module.exports = { resolvers };

