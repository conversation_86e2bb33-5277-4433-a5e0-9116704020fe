/**
 * Knex migration file for creating salary import tracking tables
 * Creates: salary_import_tracking and salary_import_errors
 * 
 * SQL Generated:
 * 
 * CREATE TABLE `salary_import_tracking` (
 *   `Import_Id` VARCHAR(36) PRIMARY KEY,
 *   `Organization_Code` VARCHAR(50) NOT NULL,
 *   `Initiated_By` INT NOT NULL,
 *   `Initiated_On` DATETIME DEFAULT CURRENT_TIMESTAMP,
 *   `Status` ENUM('In Progress', 'Completed', 'Failed') DEFAULT 'In Progress',
 *   `Total_Records` INT NOT NULL,
 *   `Successful_Records` INT DEFAULT 0,
 *   `Failed_Records` INT DEFAULT 0,
 *   `Processing_Start_Time` DATETIME,
 *   `Processing_End_Time` DATETIME,
 *   `Duration_Seconds` INT,
 *   `Notes` TEXT,
 *   `Created_On` DATETIME DEFAULT CURRENT_TIMESTAMP,
 *   `Updated_On` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 *   KEY `idx_org_initiated` (`Organization_Code`, `Initiated_By`),
 *   KEY `idx_status` (`Status`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
 * 
 * CREATE TABLE `salary_import_errors` (
 *   `Error_Id` INT AUTO_INCREMENT PRIMARY KEY,
 *   `Import_Id` VARCHAR(36) NOT NULL,
 *   `Record_Index` INT,
 *   `Form_Id` INT,
 *   `Action` VARCHAR(20),
 *   `Employee_Id` INT,
 *   `Template_Id` INT,
 *   `Revision_Id` INT,
 *   `Error_Code` VARCHAR(50),
 *   `Error_Message` TEXT,
 *   `Failed_Input` JSON,
 *   `Stack_Trace` TEXT,
 *   `Created_On` DATETIME DEFAULT CURRENT_TIMESTAMP,
 *   KEY `idx_import` (`Import_Id`),
 *   KEY `idx_form` (`Form_Id`),
 *   KEY `idx_error_code` (`Error_Code`),
 *   FOREIGN KEY (`Import_Id`) REFERENCES `salary_import_tracking`(`Import_Id`) ON DELETE CASCADE
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
 */

exports.up = function(knex) {
  return knex.schema
    // Create salary_import_tracking table
    .createTable('salary_import_tracking', function(table) {
      table.string('Import_Id', 36).primary();
      table.string('Organization_Code', 50).notNullable();
      table.integer('Initiated_By').notNullable();
      table.dateTime('Initiated_On').defaultTo(knex.fn.now());
      table.enum('Status', ['In Progress', 'Completed', 'Failed']).defaultTo('In Progress');
      table.integer('Total_Records').notNullable();
      table.integer('Successful_Records').defaultTo(0);
      table.integer('Failed_Records').defaultTo(0);
      table.dateTime('Processing_Start_Time');
      table.dateTime('Processing_End_Time');
      table.integer('Duration_Seconds');
      table.text('Notes');
      table.dateTime('Created_On').defaultTo(knex.fn.now());
      table.dateTime('Updated_On').defaultTo(knex.fn.now()).onUpdate(knex.raw('CURRENT_TIMESTAMP'));
      
      // Indexes
      table.index(['Organization_Code', 'Initiated_By'], 'idx_org_initiated');
      table.index('Status', 'idx_status');
    })
    // Create salary_import_errors table
    .createTable('salary_import_errors', function(table) {
      table.increments('Error_Id').primary();
      table.string('Import_Id', 36).notNullable();
      table.integer('Record_Index');
      table.integer('Form_Id');
      table.string('Action', 20);
      table.integer('Employee_Id');
      table.integer('Template_Id');
      table.integer('Revision_Id');
      table.string('Error_Code', 50);
      table.text('Error_Message');
      table.json('Failed_Input');
      table.text('Stack_Trace');
      table.dateTime('Created_On').defaultTo(knex.fn.now());
      
      // Indexes
      table.index('Import_Id', 'idx_import');
      table.index('Form_Id', 'idx_form');
      table.index('Error_Code', 'idx_error_code');
      
      // Foreign key
      table.foreign('Import_Id').references('salary_import_tracking.Import_Id');
    });
};

exports.down = function(knex) {
  return knex.schema
    .dropTableIfExists('salary_import_errors')
    .dropTableIfExists('salary_import_tracking');
};
