/**
 * Migration to create salary_import table for salary revision import functionality
 */

exports.up = function(knex) {
  return knex.schema.createTable('salary_import', function(table) {
    table.increments('Salary_Import_Id').primary();
    table.enum('Import_Type', ['Add', 'Revision', 'Revision With Arrears']).notNullable();
    table.string('Actual_File_Name', 50).notNullable();
    table.string('Error_File_Name', 50).notNullable();
    table.enum('Import_Status', ['Open', 'In Progress', 'Complete']).notNullable();
    table.datetime('Added_On').notNullable();
    table.integer('Added_By').notNullable();
    
    // Additional tracking fields
    table.integer('Total_Records').defaultTo(0);
    table.integer('Success_Records').defaultTo(0);
    table.integer('Failed_Records').defaultTo(0);
    table.datetime('Processing_Start_Time').nullable();
    table.datetime('Processing_End_Time').nullable();
    table.integer('Duration_Seconds').nullable();
    table.string('Organization_Code', 10).nullable();
    table.datetime('Updated_On').nullable();
    table.integer('Updated_By').nullable();
    
    // Indexes
    table.index('Import_Status');
    table.index('Added_By');
    table.index('Organization_Code');
    table.index('Added_On');
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('salary_import');
};
