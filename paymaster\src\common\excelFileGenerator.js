/**
 * Excel file generation utilities for salary import
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

/**
 * Generate original Excel file with all records
 * @param {Array} records - Array of salary import records
 * @param {number} salaryImportId - Salary Import ID for unique filename
 * @returns {Object} - { fileName, filePath, buffer }
 */
function generateOriginalFile(records, salaryImportId) {
  try {
    // Create worksheet data with headers
    const worksheetData = [
      ['Employee_Id', 'Employee_Name', 'Salary_Template', 'Previous_Annual_Ctc', 'Revise_By', 'Amount_Or_Percentage', 'Status', 'Error_Message']
    ];

    // Add all records
    records.forEach(record => {
      worksheetData.push([
        record.Employee_Id || '',
        record.Employee_Name || '',
        record.Salary_Template || '',
        record.Previous_Annual_Ctc || '',
        record.Revise_By || '',
        record.Amount_Or_Percentage || '',
        record.Status || 'Pending',
        record.Error_Message || ''
      ]);
    });

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set column widths
    worksheet['!cols'] = [
      { width: 12 }, // Employee_Id
      { width: 25 }, // Employee_Name
      { width: 20 }, // Salary_Template
      { width: 18 }, // Previous_Annual_Ctc
      { width: 12 }, // Revise_By
      { width: 18 }, // Amount_Or_Percentage
      { width: 12 }, // Status
      { width: 50 }  // Error_Message
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Salary Import');

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `salary_import_original_${salaryImportId}_${timestamp}.xlsx`;
    const tempPath = path.join(__dirname, '../../temp', fileName);

    // Ensure temp directory exists
    const tempDir = path.dirname(tempPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write file
    XLSX.writeFile(workbook, tempPath);

    // Also create buffer for S3 upload
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    return {
      fileName,
      filePath: tempPath,
      buffer
    };

  } catch (error) {
    console.error('Error generating original Excel file:', error);
    throw error;
  }
}

/**
 * Generate error Excel file with only failed records
 * @param {Array} errorRecords - Array of failed salary import records
 * @param {number} salaryImportId - Salary Import ID for unique filename
 * @returns {Object} - { fileName, filePath, buffer }
 */
function generateErrorFile(errorRecords, salaryImportId) {
  try {
    // Create worksheet data with headers
    const worksheetData = [
      ['Employee_Id', 'Employee_Name', 'Salary_Template', 'Previous_Annual_Ctc', 'Revise_By', 'Amount_Or_Percentage', 'Error_Code', 'Error_Message', 'Error_Details']
    ];

    // Add only error records
    errorRecords.forEach(record => {
      worksheetData.push([
        record.Employee_Id || '',
        record.Employee_Name || '',
        record.Salary_Template || '',
        record.Previous_Annual_Ctc || '',
        record.Revise_By || '',
        record.Amount_Or_Percentage || '',
        record.Error_Code || '',
        record.Error_Message || '',
        record.Error_Details || ''
      ]);
    });

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set column widths
    worksheet['!cols'] = [
      { width: 12 }, // Employee_Id
      { width: 25 }, // Employee_Name
      { width: 20 }, // Salary_Template
      { width: 18 }, // Previous_Annual_Ctc
      { width: 12 }, // Revise_By
      { width: 18 }, // Amount_Or_Percentage
      { width: 15 }, // Error_Code
      { width: 50 }, // Error_Message
      { width: 100 } // Error_Details
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Import Errors');

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `salary_import_errors_${salaryImportId}_${timestamp}.xlsx`;
    const tempPath = path.join(__dirname, '../../temp', fileName);

    // Ensure temp directory exists
    const tempDir = path.dirname(tempPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write file
    XLSX.writeFile(workbook, tempPath);

    // Also create buffer for S3 upload
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    return {
      fileName,
      filePath: tempPath,
      buffer
    };

  } catch (error) {
    console.error('Error generating error Excel file:', error);
    throw error;
  }
}

/**
 * Clean up temporary files
 * @param {string} filePath - Path to file to delete
 */
function cleanupTempFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Cleaned up temp file: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error cleaning up temp file ${filePath}:`, error);
  }
}

module.exports = {
  generateOriginalFile,
  generateErrorFile,
  cleanupTempFile
};
