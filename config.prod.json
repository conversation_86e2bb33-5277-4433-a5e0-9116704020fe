{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "dbSecretName": "prod/hrapp/pgaccess", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::484056187456:role/LambdaMicroservice", "dbPrefix": "hrapp_", "domainName": "hrapp", "customDomainName": "api.hrapp.co", "firebaseAuthorizer": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "profileBucket": "s3.images.hrapp.co", "documentsBucket": "s3.taxdocs.hrapp.co", "sesTemplatesRegion": "us-west-2", "sourceEmailAddress": "<EMAIL>", "logoBucket": "s3.logos.hrapp.co", "webAddress": ".co", "employeeTaxDetailUrl": "https://{orgCode}.hrapp.co/payroll/salary-payslip/get-employee-tax-details", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:484056187456:function:PAYMASTER-prod", "processCancelSalaryRevisionsFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-processCancelSalaryRevisionsFunction", "processSalaryAddUpdateFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-triggerSalaryAddUpdate", "stateMachineArn": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-bulkPayslipProcessing", "bucketName": "s3.taxdocs.hrapp.co", "bulkProcessingLambdaArn": "arn:aws:lambda:ap-south-1:484056187456:function:BULKPROCESSING-prod-graphql", "initiatePayslipRefreshArn": "arn:aws:lambda:ap-south-1:484056187456:function:prod-initiatePayslipRefresh"}