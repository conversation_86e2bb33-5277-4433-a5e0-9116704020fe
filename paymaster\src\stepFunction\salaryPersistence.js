/**
 * Salary revision persistence logic for formId 360 ONLY
 * Handles ONLY formId 360 (Salary Revision) using addUpdateSalaryDetails resolver
 */

const { ehrTables } = require('../common/tablealias');
const { calculateSalary } = require('../roresolvers/salary/calculateSalary');
const { addUpdateSalaryDetails } = require('../resolvers/addUpdateSalaryDetails');

/**
 * Persist salary revision data using addUpdateSalaryDetails resolver
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} validatedData - Validated salary revision data
 * @param {number} loginEmployeeId - Login employee ID
 * @param {string} orgCode - Organization code
 * @returns {Object} - Persistence result
 */
const persistSalaryRevision = async (organizationDbConnection, validatedData, loginEmployeeId, orgCode) => {
  try {
    console.log('=== PERSISTING SALARY REVISION (FormId 360) ===');
    console.log('Validated data:', JSON.stringify(validatedData, null, 2));
    
    // Extract validated data
    const {
      Employee_Id,
      newAnnualCtc,
      effectiveFrom,
      payoutMonth,
      allowanceDetails,
      retiralDetails,
      grossIds,
      salaryDetails,
      providentFundConfigurationValue
    } = validatedData;

    // Step 1: Call calculateSalary resolver to get calculated salary structure
    console.log('=== STEP 1: CALLING calculateSalary RESOLVER ===');
    
    // Prepare mock GraphQL context for calculateSalary resolver
    const mockContext = {
      connection: { OrganizationDb: organizationDbConnection },
      logInEmpId: loginEmployeeId,
      orgCode: orgCode
    };

    // Call calculateSalary resolver
    const calculateSalaryResult = await calculateSalary(
      null, // parent
      {
        allowanceDetails: allowanceDetails,
        employeeId: Employee_Id,
        grossIds: grossIds,
        providentFundConfigurationValue: providentFundConfigurationValue,
        retiralDetails: retiralDetails,
        revisionWithoutArrear: true,
        salaryDetails: salaryDetails
      },
      mockContext
    );

    if (calculateSalaryResult.errorCode) {
      console.error('calculateSalary failed:', calculateSalaryResult.message);
      return {
        success: false,
        errorCode: calculateSalaryResult.errorCode,
        message: calculateSalaryResult.message || 'Failed to calculate salary structure'
      };
    }

    console.log('calculateSalary completed successfully');

    // Step 2: Call addUpdateSalaryDetails resolver to persist the salary revision
    console.log('=== STEP 2: CALLING addUpdateSalaryDetails RESOLVER ===');
    
    // Prepare parameters for addUpdateSalaryDetails resolver
    const addUpdateSalaryArgs = {
      allowanceDetails: allowanceDetails,
      employeeId: Employee_Id,
      grossIds: grossIds,
      providentFundConfigurationValue: providentFundConfigurationValue,
      retiralDetails: retiralDetails,
      revisionWithoutArrear: true,
      salaryDetails: salaryDetails,
      isImport: true // Enable error capture mode
    };

    // Call addUpdateSalaryDetails resolver
    const addUpdateResult = await addUpdateSalaryDetails(
      null, // parent
      addUpdateSalaryArgs,
      mockContext
    );

    if (addUpdateResult.errorCode) {
      console.error('addUpdateSalaryDetails failed:', addUpdateResult.message);
      return {
        success: false,
        errorCode: addUpdateResult.errorCode,
        message: addUpdateResult.message || 'Failed to persist salary revision'
      };
    }

    console.log('addUpdateSalaryDetails completed successfully');

    // Step 3: Return success result
    return {
      success: true,
      persistedIds: {
        employeeSalaryId: addUpdateResult.employeeSalaryId || null,
        salaryRevisionId: addUpdateResult.salaryRevisionId || null
      },
      message: 'Salary revision persisted successfully',
      calculatedSalaryStructure: calculateSalaryResult.salaryStructure || null
    };

  } catch (error) {
    console.error('Error in persistSalaryRevision:', error);
    return {
      success: false,
      errorCode: 'SALARY_REVISION_PERSISTENCE_ERROR',
      message: error.message || 'An error occurred during salary revision persistence'
    };
  }
};

/**
 * Capture processing error for failed salary revision records
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} salaryRevisionRecord - Original salary revision record
 * @param {string} phase - Processing phase where error occurred
 * @param {string} errorCode - Error code
 * @param {string} errorMessage - Error message
 * @param {number} salaryImportId - Salary import ID
 * @returns {Object} - Error capture result
 */
const captureProcessingError = async (organizationDbConnection, salaryRevisionRecord, phase, errorCode, errorMessage, salaryImportId) => {
  try {
    console.log('=== CAPTURING PROCESSING ERROR ===');
    console.log(`Phase: ${phase}, Error: ${errorCode} - ${errorMessage}`);
    
    // Update salary_import table with error details
    await organizationDbConnection('salary_import')
      .where('Salary_Import_Id', salaryImportId)
      .increment('Failed_Records', 1)
      .update({
        Updated_On: new Date()
      });

    console.log('Processing error captured successfully');
    
    return {
      success: true,
      message: 'Processing error captured successfully'
    };

  } catch (error) {
    console.error('Error in captureProcessingError:', error);
    return {
      success: false,
      errorCode: 'ERROR_CAPTURE_FAILED',
      message: error.message || 'Failed to capture processing error'
    };
  }
};

module.exports = {
  persistSalaryRevision,
  captureProcessingError
};
