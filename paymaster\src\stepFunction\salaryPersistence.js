/**
 * PHASE 3A & 3B: Salary Persistence & Error Capture Functions
 * 
 * Phase 3A: persistSalaryData
 * - Persists validated salary to database
 * - Uses transaction for all-or-nothing writes
 * - Only called if Phase 1 and Phase 2 succeed
 * 
 * Phase 3B: captureProcessingError
 * - Captures error details when Phase 1/2/3A fails
 * - Safe write-only operation (can't corrupt)
 * - Stores original input for retry capability
 */

const knex = require('knex');

/**
 * Persist salary data to database in atomic transaction (supports all formIds)
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {Object} calculationResponse - Response from Phase 1
 * @param {Object} salaryInput - Original salary input
 * @param {string} importId - Import batch ID
 * @param {number} employeeId - Employee ID
 * @returns {Promise<Object>} - Persistence result with salaryId or error
 */
const persistSalaryData = async (organizationDbConnection, calculationResponse, salaryInput, importId, employeeId) => {
  console.log('=== PHASE 3A: PERSIST SALARY DATA ===');
  console.log(`Employee: ${employeeId}, Import: ${importId}, FormId: ${salaryInput.formId}`);

  const trx = await organizationDbConnection.transaction();

  try {
    const { salaryStructure, employeeRetiralDetails } = calculationResponse;
    const now = new Date();
    const formId = parseInt(salaryInput.formId);

    // Route to formId-specific persistence
    let salaryId;

    switch (formId) {
      case 206:
        salaryId = await persistSalaryTemplate(trx, salaryInput, salaryStructure, employeeRetiralDetails, now);
        break;
      case 207:
        salaryId = await persistSalaryDetails(trx, salaryInput, salaryStructure, employeeRetiralDetails, now);
        break;
      case 360:
        salaryId = await persistSalaryRevision(trx, salaryInput, salaryStructure, employeeRetiralDetails, now);
        break;
      default:
        throw new Error(`Unsupported formId for persistence: ${formId}`);
    }

    if (!salaryId) {
      throw new Error('Failed to get inserted salary ID');
    }

    console.log(`Salary persisted with ID: ${salaryId} for formId ${formId}`);

    // Update salary_import_employees with success status
    console.log('Updating import employee status to SUCCESS...');

    const duration = Date.now() - salaryInput.startTime;

    await trx('salary_import_employees')
      .where('Import_Id', importId)
      .where('Employee_Id', employeeId)
      .update({
        Processing_Status: 'SUCCESS',
        Salary_Id: salaryId,
        Calculated_Response: JSON.stringify(calculationResponse),
        Processed_On: now,
        Updated_On: now,
        Processing_Duration_Ms: duration
      });

    // Commit transaction (all-or-nothing)
    await trx.commit();

    console.log(`Successfully persisted salary for employee ${employeeId} with ID ${salaryId}`);

    return {
      success: true,
      salaryId: salaryId,
      errorCode: null,
      errorMessage: null
    };

  } catch (error) {
    // Rollback on any error - ensures no partial writes
    await trx.rollback();

    console.error('Error in persistSalaryData:', error);
    console.error('Transaction rolled back - no data written to database');

    return {
      success: false,
      salaryId: null,
      errorCode: 'PERSIST_ERROR',
      errorMessage: error.message || 'Failed to persist salary data'
    };
  }
};

/**
 * Persist salary template data (formId 206)
 * @param {Object} trx - Database transaction
 * @param {Object} salaryInput - Original salary input
 * @param {Object} salaryStructure - Calculated salary structure
 * @param {Object} employeeRetiralDetails - Employee retiral details
 * @param {Date} now - Current timestamp
 * @returns {Promise<number>} - Inserted template ID
 */
const persistSalaryTemplate = async (trx, salaryInput, salaryStructure, employeeRetiralDetails, now) => {
  console.log('Persisting salary template (formId 206)...');

  // Insert into salary_template table
  const templatePayload = {
    Template_Name: salaryInput.templateName || `Template_${salaryInput.employeeId}_${Date.now()}`,
    External_Template_Id: salaryInput.externalTemplateId || null,
    Annual_Ctc: salaryInput.annualCtc,
    Annual_Gross_Salary: salaryStructure.annualGross || salaryInput.annualCtc,
    Monthly_Gross_Salary: salaryStructure.monthlyCtc || (salaryInput.annualCtc / 12),
    Description: salaryInput.description || '',
    Template_Status: 'Active',
    Added_On: now,
    Added_By: salaryInput.createdBy || null
  };

  const templateResult = await trx('salary_template').insert(templatePayload);
  const templateId = templateResult[0];

  // Insert template allowances if available
  if (employeeRetiralDetails && employeeRetiralDetails.templateAllowances) {
    const allowanceData = employeeRetiralDetails.templateAllowances.map(allowance => ({
      Template_Id: templateId,
      Allowance_Type_Id: allowance.Allowance_Type_Id,
      Allowance_Type: allowance.Allowance_Type,
      Allowance_Wages: allowance.Allowance_Wages || null,
      Percentage: allowance.Percentage || null,
      Amount: allowance.Amount || null,
      FBP_Max_Declaration: allowance.FBP_Max_Declaration || null
    }));

    if (allowanceData.length > 0) {
      await trx('template_allowance_components').insert(allowanceData);
      console.log(`Inserted ${allowanceData.length} template allowance records`);
    }
  }

  // Insert template retirals if available
  if (employeeRetiralDetails && employeeRetiralDetails.templateRetirals) {
    const retiralData = employeeRetiralDetails.templateRetirals.map(retiral => ({
      Template_Id: templateId,
      Form_Id: retiral.Form_Id,
      Retirals_Id: retiral.Retirals_Id,
      Retirals_Type: retiral.Retirals_Type,
      Employee_Retiral_Wages: retiral.Employee_Retiral_Wages || null,
      Employer_Retiral_Wages: retiral.Employer_Retiral_Wages || null,
      Employee_Share_Percentage: retiral.Employee_Share_Percentage || null,
      Employer_Share_Percentage: retiral.Employer_Share_Percentage || null,
      Employee_Share_Amount: retiral.Employee_Share_Amount || null,
      Employer_Share_Amount: retiral.Employer_Share_Amount || null
    }));

    if (retiralData.length > 0) {
      await trx('template_retiral_components').insert(retiralData);
      console.log(`Inserted ${retiralData.length} template retiral records`);
    }
  }

  return templateId;
};

/**
 * Persist salary details data (formId 207)
 * @param {Object} trx - Database transaction
 * @param {Object} salaryInput - Original salary input
 * @param {Object} salaryStructure - Calculated salary structure
 * @param {Object} employeeRetiralDetails - Employee retiral details
 * @param {Date} now - Current timestamp
 * @returns {Promise<number>} - Inserted salary ID
 */
const persistSalaryDetails = async (trx, salaryInput, salaryStructure, employeeRetiralDetails, now) => {
  console.log('Persisting salary details (formId 207)...');

  // Insert into salary_salary_details (main salary record)
  const salaryDetailsPayload = {
    Employee_Id: salaryInput.employeeId,
    Template_Id: salaryInput.templateId || null,
    Effective_From: salaryInput.effectiveFrom || now,
    Annual_Ctc: salaryInput.annualCtc,
    Annual_Gross_Salary: salaryStructure.annualGross || salaryInput.annualCtc,
    Monthly_Gross_Salary: salaryStructure.monthlyCtc || (salaryInput.annualCtc / 12),
    Salary_Effective_Month: salaryInput.salaryEffectiveMonth || null,
    Added_On: now,
    Added_By: salaryInput.createdBy || null
  };

  const salaryDetailsResult = await trx('salary_salary_details').insert(salaryDetailsPayload);
  const salaryId = salaryDetailsResult[0];

  // Insert salary earnings (allowances)
  if (employeeRetiralDetails && employeeRetiralDetails.employeeSalaryAllowance) {
    const earningsData = employeeRetiralDetails.employeeSalaryAllowance
      .filter(allowance => allowance && allowance.Amount !== null && allowance.Amount !== undefined)
      .map(allowance => ({
        Employee_Id: salaryInput.employeeId,
        Allowance_Type_Id: allowance.Allowance_Type_Id,
        Allowance_Type: allowance.Allowance_Type,
        Allowance_Wages: allowance.Allowance_Wages || null,
        Percentage: allowance.Percentage || null,
        Amount: allowance.Amount,
        FBP_Max_Declaration: allowance.FBP_Max_Declaration || null
      }));

    if (earningsData.length > 0) {
      await trx('salary_earnings').insert(earningsData);
      console.log(`Inserted ${earningsData.length} salary earning records`);
    }
  }

  // Insert salary deductions (retirals)
  if (employeeRetiralDetails && employeeRetiralDetails.employeeSalaryRetirals) {
    const deductionsData = employeeRetiralDetails.employeeSalaryRetirals.map(retiral => ({
      Employee_Id: salaryInput.employeeId,
      Form_Id: retiral.Form_Id,
      Retirals_Id: retiral.Retirals_Id,
      Retirals_Type: retiral.Retirals_Type,
      Employee_Retiral_Wages: retiral.Employee_Retiral_Wages || null,
      Employer_Retiral_Wages: retiral.Employer_Retiral_Wages || null,
      Employee_Share_Percentage: retiral.Employee_Share_Percentage || null,
      Employer_Share_Percentage: retiral.Employer_Share_Percentage || null,
      Employee_Share_Amount: retiral.Employee_Share_Amount || null,
      Employer_Share_Amount: retiral.Employer_Share_Amount || null
    }));

    if (deductionsData.length > 0) {
      await trx('salary_deductions').insert(deductionsData);
      console.log(`Inserted ${deductionsData.length} salary deduction records`);
    }
  }

  return salaryId;
};

/**
 * Persist salary revision data (formId 360)
 * @param {Object} trx - Database transaction
 * @param {Object} salaryInput - Original salary input
 * @param {Object} salaryStructure - Calculated salary structure
 * @param {Object} employeeRetiralDetails - Employee retiral details
 * @param {Date} now - Current timestamp
 * @returns {Promise<number>} - Inserted revision ID
 */
const persistSalaryRevision = async (trx, salaryInput, salaryStructure, employeeRetiralDetails, now) => {
  console.log('Persisting salary revision (formId 360)...');

  // Insert into salary_revision_details
  const revisionPayload = {
    Employee_Id: salaryInput.employeeId,
    Template_Id: salaryInput.templateId,
    Salary_Effective_Month: salaryInput.salaryEffectiveMonth,
    Salary_Effective_To: salaryInput.salaryEffectiveTo || null,
    Effective_From: salaryInput.effectiveFrom || now,
    Revision_Type: salaryInput.revisionType || 'Increment',
    Revision_Status: salaryInput.revisionStatus || 'Applied',
    Previous_Ctc: salaryInput.previousCtc || 0,
    Revise_Ctc_By_Percentage: salaryInput.reviseCtcByPercentage || null,
    Payout_Month: salaryInput.payoutMonth || null,
    Annual_Ctc: salaryInput.annualCtc,
    Annual_Gross_Salary: salaryStructure.annualGross || salaryInput.annualCtc,
    Monthly_Gross_Salary: salaryStructure.monthlyCtc || (salaryInput.annualCtc / 12),
    Added_On: now,
    Added_By: salaryInput.createdBy || null
  };

  const revisionResult = await trx('salary_revision_details').insert(revisionPayload);
  const revisionId = revisionResult[0];

  // Insert revision allowances if available
  if (employeeRetiralDetails && employeeRetiralDetails.revisionAllowances) {
    const allowanceData = employeeRetiralDetails.revisionAllowances.map(allowance => ({
      Revision_Id: revisionId,
      Allowance_Type_Id: allowance.Allowance_Type_Id,
      Allowance_Type: allowance.Allowance_Type,
      Allowance_Wages: allowance.Allowance_Wages || null,
      Percentage: allowance.Percentage || null,
      Amount: allowance.Amount || null,
      FBP_Max_Declaration: allowance.FBP_Max_Declaration || null
    }));

    if (allowanceData.length > 0) {
      await trx('salary_revision_allowance').insert(allowanceData);
      console.log(`Inserted ${allowanceData.length} revision allowance records`);
    }
  }

  // Insert revision retirals if available
  if (employeeRetiralDetails && employeeRetiralDetails.revisionRetirals) {
    const retiralData = employeeRetiralDetails.revisionRetirals.map(retiral => ({
      Revision_Id: revisionId,
      Form_Id: retiral.Form_Id,
      Retirals_Id: retiral.Retirals_Id,
      Retirals_Type: retiral.Retirals_Type,
      Employee_Retiral_Wages: retiral.Employee_Retiral_Wages || null,
      Employer_Retiral_Wages: retiral.Employer_Retiral_Wages || null,
      Employee_Share_Percentage: retiral.Employee_Share_Percentage || null,
      Employer_Share_Percentage: retiral.Employer_Share_Percentage || null,
      Employee_Share_Amount: retiral.Employee_Share_Amount || null,
      Employer_Share_Amount: retiral.Employer_Share_Amount || null
    }));

    if (retiralData.length > 0) {
      await trx('salary_revision_retirals').insert(retiralData);
      console.log(`Inserted ${retiralData.length} revision retiral records`);
    }
  }

  return revisionId;
};

/**
 * Capture processing error details
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {string} importId - Import batch ID
 * @param {number} employeeId - Employee ID
 * @param {Object} salaryData - Original salary input
 * @param {string} errorCode - Error code from Phase 1 or 2
 * @param {string} errorMessage - Error message
 * @param {string} phase - Which phase failed (CALCULATE, VALIDATE, PERSIST)
 * @param {Array} validationErrors - Validation errors (if from Phase 2)
 * @returns {Promise<Object>} - Success status
 */
const captureProcessingError = async (organizationDbConnection, importId, employeeId, salaryData, errorCode, errorMessage, phase = 'CALCULATE', validationErrors = null) => {
  console.log('=== PHASE 3B: CAPTURE PROCESSING ERROR ===');
  console.log(`Employee: ${employeeId}, Import: ${importId}, Phase: ${phase}, Error: ${errorCode}`);

  try {
    const now = new Date();

    // Determine processing status based on phase
    const statusMap = {
      'CALCULATE': 'FAILED',
      'VALIDATE': 'VALIDATED',  // Got through validation but failed check
      'PERSIST': 'FAILED'
    };

    const status = statusMap[phase] || 'FAILED';

    // Update salary_import_employees with error details
    const updatePayload = {
      Processing_Status: status,
      Error_Code: errorCode,
      Error_Message: errorMessage,
      Salary_Data: JSON.stringify(salaryData),
      Processed_On: now,
      Updated_On: now
    };

    // Add validation errors if from Phase 2
    if (validationErrors && Array.isArray(validationErrors)) {
      updatePayload.Validation_Errors = JSON.stringify(validationErrors);
    }

    const updateResult = await organizationDbConnection('salary_import_employees')
      .where('Import_Id', importId)
      .where('Employee_Id', employeeId)
      .update(updatePayload);

    if (updateResult === 0) {
      console.warn(`No record found to update: Import ${importId}, Employee ${employeeId}`);
    } else {
      console.log(`Error captured successfully for employee ${employeeId}`);
    }

    return {
      success: true,
      employeeId: employeeId
    };

  } catch (error) {
    console.error('Error in captureProcessingError:', error);
    console.error('Failed to capture processing error - this is a logging failure, not a data corruption');
    
    // Even if capture fails, don't crash - just log
    throw error;
  }
};

module.exports = {
  persistSalaryData: persistSalaryData,
  captureProcessingError: captureProcessingError
};
