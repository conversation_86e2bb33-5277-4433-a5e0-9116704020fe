/**
 * PHASE 3A & 3B: Salary Persistence & Error Capture Functions
 * 
 * Phase 3A: persistSalaryData
 * - Persists validated salary to database
 * - Uses transaction for all-or-nothing writes
 * - Only called if Phase 1 and Phase 2 succeed
 * 
 * Phase 3B: captureProcessingError
 * - Captures error details when Phase 1/2/3A fails
 * - Safe write-only operation (can't corrupt)
 * - Stores original input for retry capability
 */

const knex = require('knex');

/**
 * Persist salary data to database in atomic transaction
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {Object} calculationResponse - Response from Phase 1
 * @param {Object} salaryInput - Original salary input
 * @param {string} importId - Import batch ID
 * @param {number} employeeId - Employee ID
 * @returns {Promise<Object>} - Persistence result with salaryId or error
 */
const persistSalaryData = async (organizationDbConnection, calculationResponse, salaryInput, importId, employeeId) => {
  console.log('=== PHASE 3A: PERSIST SALARY DATA ===');
  console.log(`Employee: ${employeeId}, Import: ${importId}`);

  const trx = await organizationDbConnection.transaction();

  try {
    const { salaryStructure, employeeRetiralDetails } = calculationResponse;
    const now = new Date();

    // Step 1: Insert into salary_salary_details (main salary record)
    console.log('Inserting salary details...');
    
    const salaryDetailsPayload = {
      Employee_Id: employeeId,
      Form_Id: salaryInput.formId,
      Salary_Effective_Month: salaryInput.salaryEffectiveMonth || null,
      Status: 'Active',
      Annual_Ctc: salaryInput.annualCtc,
      Created_On: now,
      Updated_On: now,
      Created_By: salaryInput.createdBy || null
    };

    const salaryDetailsResult = await trx('salary_salary_details')
      .insert(salaryDetailsPayload);

    const salaryId = salaryDetailsResult[0];
    
    if (!salaryId) {
      throw new Error('Failed to get inserted salary ID');
    }

    console.log(`Salary details inserted with ID: ${salaryId}`);

    // Step 2: Insert salary earnings (allowances)
    if (employeeRetiralDetails && employeeRetiralDetails.employeeSalaryAllowance) {
      console.log('Inserting salary allowances...');
      
      const earningsData = employeeRetiralDetails.employeeSalaryAllowance
        .filter(allowance => allowance && allowance.Amount !== null && allowance.Amount !== undefined)
        .map(allowance => ({
          Salary_Id: salaryId,
          Allowance_Type_Id: allowance.Allowance_Type_Id,
          Component_Code: allowance.Component_Code,
          Allowance_Type: allowance.Allowance_Type,
          Amount: allowance.Amount,
          Percentage: allowance.Percentage || null,
          Created_On: now,
          Updated_On: now
        }));

      if (earningsData.length > 0) {
        await trx('salary_earnings').insert(earningsData);
        console.log(`Inserted ${earningsData.length} allowance records`);
      }
    }

    // Step 3: Insert salary deductions (retirals - PF, Insurance, Gratuity, NPS, Bonus)
    if (employeeRetiralDetails) {
      console.log('Inserting salary retirals...');
      
      const deductionsData = [];

      // Insert PF details
      if (employeeRetiralDetails.employeeSalaryRetirals && Array.isArray(employeeRetiralDetails.employeeSalaryRetirals)) {
        employeeRetiralDetails.employeeSalaryRetirals.forEach(retiral => {
          deductionsData.push({
            Salary_Id: salaryId,
            Retiral_Id: retiral.Retirals_Id,
            Form_Id: retiral.Form_Id,
            Retiral_Type: retiral.Retiral_Type,
            Employee_Share_Amount: retiral.Employee_Share_Amount || 0,
            Employer_Share_Amount: retiral.Employer_Share_Amount || 0,
            Employee_Percentage: retiral.Employee_Share_Percentage || null,
            Employer_Percentage: retiral.Employer_Share_Percentage || null,
            Created_On: now,
            Updated_On: now
          });
        });
      }

      // Insert Bonus details
      if (employeeRetiralDetails.employeeSalaryBonus && Array.isArray(employeeRetiralDetails.employeeSalaryBonus)) {
        employeeRetiralDetails.employeeSalaryBonus.forEach(bonus => {
          deductionsData.push({
            Salary_Id: salaryId,
            Allowance_Type_Id: bonus.Allowance_Type_Id,
            Bonus_Type: bonus.Allowance_Type,
            Amount: bonus.Amount || 0,
            Period: bonus.Period,
            Created_On: now,
            Updated_On: now
          });
        });
      }

      if (deductionsData.length > 0) {
        await trx('salary_deductions').insert(deductionsData);
        console.log(`Inserted ${deductionsData.length} deduction records`);
      }
    }

    // Step 4: Update salary_import_employees with success status
    console.log('Updating import employee status to SUCCESS...');
    
    const duration = Date.now() - salaryInput.startTime;
    
    await trx('salary_import_employees')
      .where('Import_Id', importId)
      .where('Employee_Id', employeeId)
      .update({
        Processing_Status: 'SUCCESS',
        Salary_Id: salaryId,
        Calculated_Response: JSON.stringify(calculationResponse),
        Processed_On: now,
        Updated_On: now,
        Processing_Duration_Ms: duration
      });

    // Step 5: Commit transaction (all-or-nothing)
    await trx.commit();

    console.log(`Successfully persisted salary for employee ${employeeId} with ID ${salaryId}`);

    return {
      success: true,
      salaryId: salaryId,
      errorCode: null,
      errorMessage: null
    };

  } catch (error) {
    // Rollback on any error - ensures no partial writes
    await trx.rollback();
    
    console.error('Error in persistSalaryData:', error);
    console.error('Transaction rolled back - no data written to database');

    return {
      success: false,
      salaryId: null,
      errorCode: 'PERSIST_ERROR',
      errorMessage: error.message || 'Failed to persist salary data'
    };
  }
};

/**
 * Capture processing error details
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {string} importId - Import batch ID
 * @param {number} employeeId - Employee ID
 * @param {Object} salaryData - Original salary input
 * @param {string} errorCode - Error code from Phase 1 or 2
 * @param {string} errorMessage - Error message
 * @param {string} phase - Which phase failed (CALCULATE, VALIDATE, PERSIST)
 * @param {Array} validationErrors - Validation errors (if from Phase 2)
 * @returns {Promise<Object>} - Success status
 */
const captureProcessingError = async (organizationDbConnection, importId, employeeId, salaryData, errorCode, errorMessage, phase = 'CALCULATE', validationErrors = null) => {
  console.log('=== PHASE 3B: CAPTURE PROCESSING ERROR ===');
  console.log(`Employee: ${employeeId}, Import: ${importId}, Phase: ${phase}, Error: ${errorCode}`);

  try {
    const now = new Date();

    // Determine processing status based on phase
    const statusMap = {
      'CALCULATE': 'FAILED',
      'VALIDATE': 'VALIDATED',  // Got through validation but failed check
      'PERSIST': 'FAILED'
    };

    const status = statusMap[phase] || 'FAILED';

    // Update salary_import_employees with error details
    const updatePayload = {
      Processing_Status: status,
      Error_Code: errorCode,
      Error_Message: errorMessage,
      Salary_Data: JSON.stringify(salaryData),
      Processed_On: now,
      Updated_On: now
    };

    // Add validation errors if from Phase 2
    if (validationErrors && Array.isArray(validationErrors)) {
      updatePayload.Validation_Errors = JSON.stringify(validationErrors);
    }

    const updateResult = await organizationDbConnection('salary_import_employees')
      .where('Import_Id', importId)
      .where('Employee_Id', employeeId)
      .update(updatePayload);

    if (updateResult === 0) {
      console.warn(`No record found to update: Import ${importId}, Employee ${employeeId}`);
    } else {
      console.log(`Error captured successfully for employee ${employeeId}`);
    }

    return {
      success: true,
      employeeId: employeeId
    };

  } catch (error) {
    console.error('Error in captureProcessingError:', error);
    console.error('Failed to capture processing error - this is a logging failure, not a data corruption');
    
    // Even if capture fails, don't crash - just log
    throw error;
  }
};

module.exports = {
  persistSalaryData: persistSalaryData,
  captureProcessingError: captureProcessingError
};
