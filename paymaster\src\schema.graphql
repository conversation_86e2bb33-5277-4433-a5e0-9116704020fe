# defining custom data type
scalar Date

enum declarationType{
  NPS
  FBP
}

type Query {
  listSalaryTemplateDetails(
    formId: Int!
    accessFormId: Int
    templateId: Int
    id: Int
    employeeId: Int
    isViewMode: Boolean
    isDropdown: Boolean
    includeHistoricalRecords: Boolean
  ): listSalaryTemplateResponse!
  listSalaryComponents(employeeId: Int): listSalaryComponentsResponse!
  getRetiralComponents: getRetiralComponentsResponse!
  getSalaryDetails(employeeId: Int!): getSalaryDetailsResponse!
  retrieveSalaryConfiguration(
    employeeId: Int!
  ): retrieveSalaryConfigurationResponse!
  getEffectiveDate(employeeId: Int!, action: String!): getEffectiveDateResponse!
  listGrossConfiguration(
    formId: Int
  ): listGrossConfigurationResponse!

  listAllowanceType(formId: Int!): listAllowanceTypeResponse!
  getFBPDeclarationSettings(typeOfDeclaration: declarationType): getFBPDeclarationSettingsResponse!
  getAllowanceWithRegime(formId: Int!): getAllowanceWithRegimeResponse!
  getEmployeeEffectiveDate(employeeId: Int!, operation: String!, formId: Int!): getEmployeeEffectiveDateResponse!
  getPerquisites(formId: Int!): getPerquisitesResponse!
  getBenefitForms(formId: Int!, all: Boolean): getBenefitFormsResponse!

  # Adhoc Allowance Types
  listAdhocAllowanceTypes(formId: Int!, status: String, isCandidateForm: Boolean): listAdhocAllowanceTypesResponse!
  getAdhocAllowanceType(adhocAllowanceTypeId: Int!, formId: Int!): getAdhocAllowanceTypeResponse!

  # Formula System APIs
  listFormulaComponents(formId: Int!, forEarningType: Int, employeeId: Int , candidateId: Int, componentTypeFilter: String): listFormulaComponentsResponse!
  listFormulaFunctions(formId: Int!): listFormulaFunctionsResponse!
  listFormulaOperators(formId: Int!): listFormulaOperatorsResponse!
  validateSalaryFormula(formula: String!, checkRuntimeComps: Boolean): validateSalaryFormulaResponse!

  # One Time Earnings APIs
  listOneTimeEarnings(formId: Int!, employeeId: Int, status: String): listOneTimeEarningsResponse!
  listBenefitAndDeductionByMonth(formId: Int!, month: Int!, year: Int!, action: String!, employeeIds: [Int], payOutPeriod: String, status: String): listOneTimeEarningsResponse!
  getOneTimeEarning(oneTimeEarningId: Int!, formId: Int!): getOneTimeEarningResponse!
  calculateOneTimeEarningAmount(formId: Int!, employeeId: Int!, formula: String!): CalculateOneTimeEarningAmountResponse!
  getDefaultPayoutMonth(formId: Int!, employeeId: Int!, oneTimeEarningTypeId: Int!): GetDefaultPayoutMonthResponse!

  # Candidate One Time Earnings APIs
  listCandidateOneTimeEarnings(formId: Int!, candidateId: Int, approvalStatus: String): listCandidateOneTimeEarningsResponse!
  getCandidateOneTimeEarning(formId: Int!, candidateOneTimeEarningId: Int!): getCandidateOneTimeEarningResponse!
  calculateCandidateOneTimeEarningAmount(formId: Int!, candidateId: Int!, formula: String!): CalculateCandidateOneTimeEarningAmountResponse!

  # Candidate Salary Queries
  listCandidateSalaryDetails(
    candidateId: Int
    templateId: Int
    formId: Int
    accessFormId: Int
    isViewMode: Boolean
    limit: Int
    offset: Int
  ): CandidateSalaryResponse!

  # Payroll Reconciliation Query
  getPayrollReconciliation(
    formId: Int!
    payslipMonth: Int!
    payslipYear: Int!
    employeeId: Int
    limit: Int
    offset: Int
  ): PayrollReconciliationResponse!

  calculateCandidateSalary(
    candidateId: Int!
    allowanceDetails: String
    retiralDetails: String
    salaryDetails: String
    providentFundConfigurationValue: String
  ): CandidateSalaryCalculationResponse!
}
type Mutation {
  deleteSalaryTemplate(templateId: Int!): deleteSalaryTemplateResponse!
  updateSalaryConfiguration(
    employeeId: Int!
    eligibleForOvertime: Int
    eligibleForPf: Int
    eligibleForPension: Int!
    exemptEDLI: Int
    UAN: String
    PfPolicyNo: String
    eligibleForESI: Int
    ESINumber: String
    eligibleForInsurance: Int
    eligibleForNps: Int
    NpsNumber: String
    eligibleForGratuity: Int
    eligibleForPT: Int
    eligibleForVpf: Int!
    vpfType: String
    vpfEmployeeShare: Float
    vpfEmployeeShareAmount: Float
    bondRecoveryApplicable: String
    minimumMonthsToBeServed: Int
    bondValue: Float
  ): updateSalaryConfigurationResponse!
  addSalaryTemplate(
    isEditForm: Int!
    templateId: Int
    templateName: String!
    annualCTC: String!
    basicPayType: String!
    percentage: String
    amount: String
    description: String
    allowance: [allowanceInput]
    retirals: [templateRetiralsInput]
  ): addSalaryTemplateResponse!
  updateTemplateStatus(
    templateId: Int!
    templateStatus: String!
  ): updateTemplateStatusResponse!
  addSalaryDetails(
    isEditForm: Int!
    employeeId: Int!
    templateId: Int!
    basicPay: String!
    effectiveFrom: String!
    effectiveTo: String
    annualCTC: String!
    annualGrossSalary: String!
    monthlyGrossSalary: String!
    allowance: [allowanceInput]
    retirals: [retiralsInput]
  ): addSalaryDetailsResponse!

  addUpdateSalaryDetails(
    # Common parameters
    formId: Int!
    accessFormId: Int!
    isEditMode: Boolean
    id: Int
    annualCTC: String
    allowance: [allowanceInput]

    # Salary Template parameters (formId: 206)
    templateName: String
    externalTemplateId: String
    description: String
    templateStatus: String

    # Salary Form and Revision parameters (formId: 207, 360)
    employeeId: Int
    templateId: Int
    effectiveFrom: String
    effectiveTo: String
    annualGrossSalary: String
    monthlyGrossSalary: String
    salaryEffectiveMonth: String
    retirals: [retiralsInput]
    gross: [grossInput]

    # Salary Revision specific parameters (formId: 360)
    payoutMonth: String
    salaryEffectiveTo: String
    revisionType: String
    revisionStatus: String
    reviseCtcByPercentage: String
    previousCtc: String

    # ESI Contribution End Date parameters
    revisionWithoutArrear: Boolean
  ): addSalaryDetailsResponse!

  deleteSalaryRecord(
    id: Int
    formId: Int!
    employeeId: Int
  ): deleteSalaryRecordResponse!
  addUpdateAllowanceType(
    formId: Int!
    allowanceTypeId: Int
    allowanceName: String!
    taxInclusion: String
    formulaBased: String
    allowanceMode: String
    period: String
    isFlexiBenefitPlan: String
    isClaimFromReimbursement: String
    allowanceTypeStatus: String
    reimbursementType: String
    unclaimedLTA: String
    epfContribution: String
    description: String
    asIsPayment : String
    benefitAssociation: [Int]
    isBasicPay: String
    allowanceType: String
    amount: Float
    percentage: Float
    fbpMaxDeclaration: Float
    restrictEmployeeFbpOverride: String
    perquisitesId: Int
    ltaStartYear: Int
    endMonth: String
    numberOfClaims: Int
    allowanceSequence: Int
    workflowId: Int
    calculationType: String
    customFormula: String
    salaryComponentId: Int
    isCustomComponent: Boolean
    nameInPayslip: String
  ): addUpdateAllowanceTypeResponse!
  deleteAllowanceType(
    allowanceTypeId: Int!
    formId: Int!
  ): deleteAllowanceTypeResponse!

  # Adhoc Allowance Types Mutations
  addUpdateAdhocAllowanceType(
    formId: Int!
    adhocAllowanceTypeId: Int
    adhocAllowanceTypeTitle: String!
    earningTypeCode: String
    customFormula: String!
    clauseId: Int
    overrideCustomFormula: String
    commitmentPeriod: String
    commitmentPeriodMonths: Int
    commitmentPeriodReference: String
    clawbackFormula: String
    defaultPayoutMonthFrom: String!
    contributionType: String
    defaultPayoutDurationMonths: Int
    autoApproval: String!
    taxInclusion: String!
    benefitAssociation: [Int]
    status: String!
    nameInPayslip: String
    adhocAllowanceTypeDescription: String
  ): addUpdateAdhocAllowanceTypeResponse!

  deleteAdhocAllowanceType(
    adhocAllowanceTypeId: Int!
    formId: Int!
  ): deleteAdhocAllowanceTypeResponse!

  # One Time Earnings Mutations
  addUpdateOneTimeEarning(
    formId: Int!
    oneTimeEarningId: Int
    employeeId: Int!
    oneTimeEarningTypeId: Int!
    amount: Float
    calculatedAmount: Float
    calculatedClawbackAmount: Float
    customFormula: String
    payoutMonth: String!
    payoutPeriod: String
    payoutEndMonth: String
    commitmentPeriodMonths: Int
    commitmentStartMonth: String
    commitmentEndMonth: String
    clawbackMonth: String
    clawbackAmount: Float
    clawbackFormula: String
    clawbackPeriod: String
  ): commonResponse!

  deleteOneTimeEarning(
    oneTimeEarningId: Int!
    formId: Int!
  ): deleteOneTimeEarningResponse!

  addUpdateGrossConfiguration(
    grossId: Int
    salaryComponentId: Int!
    grossName: String!
    displayName: String
    calculationType: String!
    customFormula: String
    status: String
    description: String
    formId: Int
  ): commonResponse

  # Formula Component Management
  addFormulaComponent(
    formId: Int!
    componentName: String!
    componentType: String!
    description: String
  ): addFormulaComponentResponse!

  deleteGrossConfiguration(
    formId: Int
    grossId: Int!
  ): commonResponse

  # Bulk Salary Revision Cancellation (Step Function)
  triggerCancelSalaryRevisions(
    formId: Int!
    revisionIds: [Int!]!
  ): TriggerCancelSalaryRevisionsResponse!

  # Bulk Salary Add/Update (Step Function)
  triggerSalaryAddUpdate(
    salaryRecords: [SalaryRecordInput!]!
  ): TriggerSalaryAddUpdateResponse!

  # Candidate One Time Earnings Mutations
  addUpdateCandidateOneTimeEarning(
    formId: Int!
    candidateOneTimeEarningId: Int
    candidateId: Int!
    oneTimeEarningTypeId: Int!
    payoutMonth: String
    payoutEndMonth: String
    payoutPeriod: String
    amount: Float!
    calculatedAmount: Float
    calculatedClawbackAmount: Float
    customFormula: String
    commitmentPeriodMonths: Int
    commitmentStartMonth: String
    commitmentEndMonth: String
    clawbackAmount: Float
    clawbackFormula: String
    clawbackPeriod: String
  ): addUpdateCandidateOneTimeEarningResponse!

  deleteCandidateOneTimeEarning(
    formId: Int!
    candidateOneTimeEarningId: Int!
  ): commonResponse!

  # Candidate Salary Mutations
  addUpdateCandidateSalaryDetails(
    formId: Int
    isEditMode: Boolean!
    candidateId: Int!
    templateId: Int
    effectiveFrom: String
    annualCTC: String!
    annualGrossSalary: String!
    monthlyGrossSalary: String!
    salaryEffectiveMonth: String
    allowance: [CandidateAllowanceInput]
    retirals: [CandidateRetiralInput]
    gross: [CandidateGrossInput]
  ): CandidateSalaryMutationResponse!

  deleteCandidateSalaryDetails(
    formId: Int
    candidateIds: [Int]!
  ): CandidateSalaryMutationResponse!
}

input allowanceInput {
  allowanceTypeId: Int!
  allowanceType: String!
  allowanceWages: String
  fbpMaxDeclaration: String
  amount: String
  percentage: String
}

input templateRetiralsInput {
  formId: String!
  retiralsId: String
  retiralsType: String!
  employeeRetiralWages: String
  employerRetiralWages: String
  employerSharePercentage: String
  employerShareAmount: String
  pfEmployerContribution: String
  employerStatutoryLimit: String
  eligibleForEPS: Int!
  adminCharge: String
  edliCharge: String
  contributionEndMonth: String
  contributionPeriodCompleted: String
}

input retiralsInput {
  formId: String!
  retiralsId: String
  retiralsType: String!
  employeeRetiralWages: String
  employerRetiralWages: String
  employeeSharePercentage: String
  employerSharePercentage: String
  employeeShareAmount: String
  employerShareAmount: String
  pfEmployeeContribution: String
  pfEmployerContribution: String
  employeeStatutoryLimit: String
  employerStatutoryLimit: String
  eligibleForEPS: Int!
  contributeEpfActualPfWage: Int
  adminCharge: String
  edliCharge: String
  contributionEndMonth: String
  contributionPeriodCompleted: String
}

input grossInput {
  grossId: Int
  amount: Float
}

input SalaryRecordInput {
  formId: Int!
  isEditMode: Boolean
  employeeId: Int
  templateId: Int
  revisionId: Int
  annualCtc: Float
  effectiveFrom: String
  salaryEffectiveMonth: String
  allowances: [allowanceInput]
  retirals: [retiralsInput]
  gross: [grossInput]
}

type listSalaryTemplateResponse {
  errorCode: String
  message: String
  currencySymbol: String
  templateDetails: String
  roundOffSettings: String
  pfSettings: String
  fiscalMonthArray: String
}

type deleteSalaryTemplateResponse {
  errorCode: String
  message: String
}

type listSalaryComponentsResponse {
  errorCode: String
  message: String
  salaryComponents: String
}

type listAllowanceTypeResponse {
  errorCode: String
  message: String
  allowanceTypes: String
}

type getFBPDeclarationSettingsResponse {
  errorCode: String
  message: String
  success: Boolean
  lockStatus: lockStatus
}

type lockStatus {
    status: String
    lockDate: String
}

type getEmployeeEffectiveDateResponse {
  errorCode: String
  message: String
  success: Boolean
  effectiveDate: String
  maxPayslipMonth: String
  overtimeAllocation: String
  wageIndex: String
  overtimeAmount: String
}

type getRetiralComponentsResponse {
  errorCode: String
  message: String
  retirals: [retiralsList]
}

type retiralsList {
  formId: Int
  formName: String
}

type updateSalaryConfigurationResponse {
  errorCode: String
  message: String
}

type addSalaryTemplateResponse {
  errorCode: String
  message: String
}

type getSalaryDetailsResponse {
  errorCode: String
  message: String
  salaryDetails: String
  currencySymbol: String
}

type updateTemplateStatusResponse {
  errorCode: String
  message: String
}

type retrieveSalaryConfigurationResponse {
  errorCode: String
  message: String
  salaryConfigurationDetails: [salaryConfigurationDetails]
  retiralsDetails: retiralsDetails
}

type salaryConfigurationDetails {
  Eligible_For_Overtime: Int
  Eligible_For_Pf: Int
  Eligible_For_Pension: Int
  Exempt_EDLI: Int
  UAN: String
  Pf_PolicyNo: String
  Eligible_For_ESI: Int
  ESI_Number: String
  Eligible_For_Insurance: Int
  Eligible_For_Nps: Int
  Nps_Number: String
  Eligible_For_Gratuity: Int
  Eligible_For_PT: Int
  Eligible_For_Vpf: Int
  Vpf_Type: String
  Vpf_Employee_Share: Float
  Vpf_Employee_Share_Amount: Float
  Bond_Recovery_Applicable: String
  Minimum_Months_To_Be_Served: Int
  Bond_Value: Float
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}

type addSalaryDetailsResponse {
  errorCode: String
  message: String
}

type deleteSalaryRecordResponse {
  errorCode: String
  message: String
}

type addUpdateAllowanceTypeResponse {
  errorCode: String
  message: String
  success: Boolean
  allowanceTypeId: Int
}

type deleteAllowanceTypeResponse {
  errorCode: String
  message: String
  success: Boolean
  allowanceTypeId: Int
}

type getPerquisitesResponse {
  errorCode: String
  message: String
  success: Boolean
  perquisites: String
}

type getBenefitFormsResponse {
  errorCode: String
  message: String
  success: Boolean
  benefitForms: String
}

type retiralsDetails {
  providentFundExist: Int
  professionalTaxExist: Int
}

type getEffectiveDateResponse {
  errorCode: String
  message: String
  effectiveDate: String
  effectiveDateRange: [String]
}

# Formula System Response Types
type listFormulaComponentsResponse {
  errorCode: String
  message: String
  success: Boolean
  data: FormulaComponentGroups
}

type listFormulaFunctionsResponse {
  errorCode: String
  message: String
  success: Boolean
  data: [FormulaFunction]
}

type listFormulaOperatorsResponse {
  errorCode: String
  message: String
  success: Boolean
  data: [FormulaOperator]
}

type validateSalaryFormulaResponse {
  errorCode: String
  message: String
  success: Boolean
  isValid: Boolean
  errors: [String]
  hasRuntimeComponents: Boolean
  runtimeComponentsFound: [String]
}

type FormulaComponent {
  componentId: Int
  componentCode: String
  componentName: String
  componentType: String
  description: String,
  groupDetails: String
  notes: String
  componentsVisibleFor: String
}

type FormulaComponentGroups {
  earning: [FormulaComponent]
  reimbursement: [FormulaComponent]
  bonus: [FormulaComponent]
  gross: [FormulaComponent]
  retiral: [FormulaComponent]
  ctc: [FormulaComponent]
  leave: [FormulaComponent]
  timeVariables: [FormulaComponent]
}

type FormulaFunction {
  functionId: Int
  name: String
  category: String
  minParams: Int
  maxParams: Int
  syntax: String
  description: String
  example: String
}

type FormulaOperator {
  operatorId: Int
  symbol: String
  name: String
  returnDataType: String
}

type addFormulaComponentResponse {
  errorCode: String
  message: String
  success: Boolean
  data: FormulaComponent
}

type revisionPayslipComponent {
  Allowance_Id: Int
  Allowance_Name: String
  Amount: String
}

type retiralsPayslipComponent {
  Form_Id: String
  Retirals_Id: String
  Employee_Share_Amount: String
  Employer_Share_Amount: String
  Insurance_Name: String
  Insurance_Type: String
  Override_Insurance_Contribution_At_Employee_Level: String
}
type CandidateSalaryDetails {
  Candidate_Id: Int
  Template_Id: Int
  Template_Name: String
  CandidateName: String
  Annual_CTC: Float
  Annual_Gross_Salary: Float
  Monthly_Gross_Salary: Float
  Salary_Effective_Month: String
  Basic_Pay: Float
  Effective_From: String
  Effective_To: String
  Added_On: String
  Updated_On: String
  AddedByName: String
  UpdatedByName: String
  allowances: CandidateAllowanceCategories
  retirals: [CandidateRetiralDetails]
}

type CandidateAllowanceCategories {
  allowanceArray: [CandidateAllowanceDetails]
  fixedAllowanceArray: [CandidateAllowanceDetails]
  bonusArray: [CandidateAllowanceDetails]
  flexiBenefitPlanArray: [CandidateAllowanceDetails]
  reimbursementArray: [CandidateAllowanceDetails]
  basicPayArray: [CandidateAllowanceDetails]
}

type CandidateAllowanceDetails {
  Allowance_Type_Id: Int
  Allowance_Name: String
  Allowance_Type: String
  Percentage: Float
  Amount: Float
  Allowance_Wages: String
  Period: String
  Tax_Inclusion: String
  Formula_Based: String
  Allowance_Mode: String
  Is_Claim_From_Reimbursement: String
  Is_Flexi_Benefit_Plan: String
  Is_Basic_Pay: String
  FBP_Max_Declaration: Float
  Form_Id: String
  Allowance_Ids: String
}

type CandidateRetiralDetails {
  Candidate_Retirals_Id: Int
  Form_Id: Int
  Retirals_Type_Id: Int
  Retirals_Type: String
  Retiral_Wages: String
  Employee_Share_Percentage: Float
  Employer_Share_Percentage: Float
  Employee_Share_Amount: Float
  Employer_Share_Amount: Float
  PF_Employee_Contribution: Float
  PF_Employer_Contribution: Float
  Employee_Statutory_Limit: Float
  Employer_Statutory_Limit: Float
  Eligible_For_EPS: String
  Contribute_EPF_Actual_PF_Wage: Int
  Admin_Charge: Float
  EDLI_Charge: Float
  Insurance_Name: String
  Insurance_Type: String
  Override_Insurance_Contribution_At_Employee_Level: String
  Payment_Frequency: String
  Retirals_Name: String
  Slab_Wise: String
  Allowance_Ids: String
  Override_PF_Contribution_Rate_At_Employee_Level: String
}



type CandidateSalaryResponse {
  errorCode: String
  message: String
  currencySymbol: String
  candidateSalaryDetails: String
  roundOffSettings: String
  pfSettings: String
}

type CandidateSalaryMutationResponse {
  errorCode: String
  message: String
}

type CandidateSalaryCalculationResponse {
  errorCode: String
  message: String
  candidateRetiralDetails: String
  salaryStructure: String
  grossSalary: Float
  netSalary: Float
  totalDeductions: Float
}

input CandidateAllowanceInput {
  allowanceTypeId: Int!
  allowanceType: String
  percentage: Float
  amount: Float!
  allowanceWages: String
  fbpMaxDeclaration: Float
}

input CandidateRetiralInput {
  formId: String!
  retiralsId: String
  retiralsType: String!
  employeeRetiralWages: String
  employerRetiralWages: String
  employeeSharePercentage: String
  employerSharePercentage: String
  employeeShareAmount: String
  employerShareAmount: String
  pfEmployeeContribution: String
  pfEmployerContribution: String
  employeeStatutoryLimit: String
  employerStatutoryLimit: String
  eligibleForEPS: Int!
  contributeEpfActualPfWage: Int
  adminCharge: String
  edliCharge: String
  contributionEndMonth: String
  contributionPeriodCompleted: String
}

input CandidateGrossInput {
  grossId: Int!
  amount: Float!
}

type commonResponse {
  errorCode: String
  message: String
  success: Boolean
}

type listGrossConfigurationResponse {
  errorCode: String
  message: String
  grossConfigurations: String
}

type getAllowanceWithRegimeResponse {
  errorCode: String
  message: String
  allowanceWithRegime: String
}

type TriggerCancelSalaryRevisionsResponse {
  errorCode: String
  message: String
  cancellationStrategy: String
  totalCount: Int
  processorResult: ProcessorResult
}

type TriggerSalaryAddUpdateResponse {
  success: Boolean!
  importId: String!
  executionArn: String!
  totalRecords: Int!
  message: String!
}

type ProcessorResult {
  statusCode: Int
  success: Boolean
  message: String
  successCount: Int
  failureCount: Int
  results: [CancellationResult]
}

type CancellationResult {
  revisionId: Int
  employeeId: Int
  success: Boolean
  errorCode: String
  errorMessage: String
}

# Adhoc Allowance Types Response Types
type listAdhocAllowanceTypesResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  adhocAllowanceTypes: String
}

type getAdhocAllowanceTypeResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  adhocAllowanceType: String
}

type addUpdateAdhocAllowanceTypeResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  adhocAllowanceTypeId: Int
}

type deleteAdhocAllowanceTypeResponse {
  errorCode: String!
  message: String!
  success: Boolean!
}
# Payroll Reconciliation Response Type
type PayrollReconciliationResponse {
  errorCode: String
  message: String
  success: Boolean
  msg: String
  payrollReconciliation: String
}

# One Time Earnings Response Types
type listOneTimeEarningsResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  oneTimeEarnings: String
}

type getOneTimeEarningResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  oneTimeEarning: String
}

type deleteOneTimeEarningResponse {
  errorCode: String!
  message: String!
  success: Boolean!
}

type CalculateOneTimeEarningAmountResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  amount: Float
}

# Candidate One Time Earnings Response Types
type listCandidateOneTimeEarningsResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  candidateOneTimeEarnings: String
}

type getCandidateOneTimeEarningResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  candidateOneTimeEarning: String
}

type addUpdateCandidateOneTimeEarningResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  candidateOneTimeEarningId: Int
  approvalStatus: String
}

type CalculateCandidateOneTimeEarningAmountResponse {
  errorCode: String!
  message: String!
  success: Boolean!
  amount: Float
}

# Get Default Payout Month Response Type
type GetDefaultPayoutMonthResponse {
  success: Boolean!
  message: String!
  errorCode: String
  defaultPayoutMonth: String
  commitmentStartMonth: String
  commitmentEndMonth: String
}

schema {
  query: Query
  mutation: Mutation
}
