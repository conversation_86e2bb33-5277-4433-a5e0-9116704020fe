/**
 * Migration: Create salary_import_employees table
 * 
 * This table tracks individual employee records within a salary import batch
 * One-to-many relationship with salary_import_tracking (1 import → many employees)
 * 
 * Fields:
 * - Id: Unique record ID
 * - Import_Id: Foreign key to salary_import_tracking (which import batch)
 * - Employee_Id: Which employee
 * - Salary_Data: Original input data (JSON)
 * - Calculated_Response: Response from calculateSalary (JSON)
 * - Validation_Errors: Validation errors if any (JSON array)
 * - Error_Code: Error code if failed
 * - Error_Message: Error message details
 * - Salary_Id: FK to salary_salary_details (only on success)
 * - Processing_Status: PENDING → CALCULATED → VALIDATED → SUCCESS/FAILED
 * - Processing_Duration_Ms: Time taken to process
 * - Created_On, Updated_On, Processed_On: Timestamps
 */

exports.up = async function(knex) {
  const sql = `
    CREATE TABLE salary_import_employees (
      Id INT AUTO_INCREMENT PRIMARY KEY,
      Import_Id VARCHAR(36) NOT NULL,
      Employee_Id INT NOT NULL,
      Salary_Id INT NULL,
      Salary_Data JSON NULL COMMENT 'Original salary input for audit/retry',
      Calculated_Response JSON NULL COMMENT 'Response from calculateSalary resolver',
      Validation_Errors JSON NULL COMMENT 'Array of validation errors from Phase 2',
      Error_Code VARCHAR(50) NULL,
      Error_Message TEXT NULL,
      Processing_Status ENUM('PENDING', 'CALCULATED', 'VALIDATED', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'PENDING',
      Processing_Duration_Ms INT NULL,
      Created_On DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      Updated_On DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      Processed_On DATETIME NULL COMMENT 'When Phase 3 completed',
      
      CONSTRAINT fk_import_id FOREIGN KEY (Import_Id) 
        REFERENCES salary_import_tracking(Import_Id) ON DELETE CASCADE,
      CONSTRAINT fk_salary_id FOREIGN KEY (Salary_Id) 
        REFERENCES salary_salary_details(Id) ON DELETE SET NULL,
      
      INDEX idx_import_employee (Import_Id, Employee_Id),
      INDEX idx_import_status (Import_Id, Processing_Status),
      INDEX idx_error_code (Error_Code),
      INDEX idx_created (Created_On)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    COMMENT='Tracks each employee salary record through 3-phase import: Calculate → Validate → Persist';
  `;
  
  return knex.raw(sql);
};

exports.down = async function(knex) {
  return knex.raw('DROP TABLE IF EXISTS salary_import_employees;');
};
