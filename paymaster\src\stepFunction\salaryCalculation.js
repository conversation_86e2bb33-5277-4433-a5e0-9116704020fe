/**
 * PHASE 1: Pure Salary Calculation Function
 * 
 * Responsibility: Calculate salary structure from input parameters
 * - NO database writes
 * - NO side effects
 * - Returns error object instead of throwing
 * 
 * Input: Salary details (formId, employeeId, ctc, allowances, retirals, etc)
 * Output: {success, salaryStructure, employeeRetiralDetails, errorCode?, errorMessage?}
 */

const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { calculateSalary } = require('../roresolvers/salary/calculateSalary');

/**
 * Calculate employee salary based on input parameters
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {Object} salaryInput - Salary input parameters
 * @returns {Promise<Object>} - Calculation result with salary structure or error
 */
const calculateEmployeeSalary = async (organizationDbConnection, salaryInput) => {
  try {
    console.log('=== PHASE 1: CALCULATE SALARY ===');
    console.log(`Employee: ${salaryInput.employeeId}, Form: ${salaryInput.formId}`);

    // Step 1: Validate required input parameters
    if (!salaryInput.employeeId) {
      return {
        success: false,
        errorCode: 'IVE0001',
        errorMessage: 'Missing required field: employeeId'
      };
    }

    if (!salaryInput.annualCtc) {
      return {
        success: false,
        errorCode: 'IVE0002',
        errorMessage: 'Missing required field: annualCtc'
      };
    }

    if (!salaryInput.formId) {
      return {
        success: false,
        errorCode: 'IVE0003',
        errorMessage: 'Missing required field: formId'
      };
    }

    // Step 2: Prepare parameters for calculateSalary resolver
    const allowanceDetails = salaryInput.allowances 
      ? JSON.stringify(salaryInput.allowances) 
      : '[]';
    
    const retiralDetails = salaryInput.retirals 
      ? JSON.stringify(salaryInput.retirals) 
      : '[]';
    
    const salaryDetails = {
      Annual_Ctc: salaryInput.annualCtc,
      Salary_Effective_Month: salaryInput.salaryEffectiveMonth || null
    };

    // Step 3: Prepare mock GraphQL context for resolver compatibility
    const mockContext = {
      connection: { OrganizationDb: organizationDbConnection },
      orgdb: organizationDbConnection
    };

    // Step 4: Call calculateSalary resolver (from existing roresolvers)
    console.log('Calling calculateSalary resolver...');
    
    const calculateResult = await calculateSalary(null, {
      employeeId: salaryInput.employeeId,
      candidateId: salaryInput.candidateId || null,
      allowanceDetails: allowanceDetails,
      retiralDetails: retiralDetails,
      salaryDetails: salaryDetails,
      revisionWithoutArrear: salaryInput.revisionWithoutArrear || false,
      grossIds: salaryInput.grossIds || []
    }, mockContext);

    // Step 5: Check if resolver returned calculation error
    if (calculateResult.errorCode) {
      console.log(`Calculation failed: ${calculateResult.errorCode}`);
      
      return {
        success: false,
        errorCode: calculateResult.errorCode,
        errorMessage: calculateResult.message || 'Salary calculation failed'
      };
    }

    // Step 6: Extract and parse calculated data
    let salaryStructure = {};
    let employeeRetiralDetails = {};

    try {
      if (calculateResult.salaryStructure) {
        salaryStructure = typeof calculateResult.salaryStructure === 'string'
          ? JSON.parse(calculateResult.salaryStructure)
          : calculateResult.salaryStructure;
      }

      if (calculateResult.employeeRetiralDetails) {
        employeeRetiralDetails = typeof calculateResult.employeeRetiralDetails === 'string'
          ? JSON.parse(calculateResult.employeeRetiralDetails)
          : calculateResult.employeeRetiralDetails;
      }
    } catch (parseError) {
      console.error('Error parsing calculated response:', parseError);
      
      return {
        success: false,
        errorCode: 'PARSE_ERROR',
        errorMessage: 'Failed to parse salary calculation response'
      };
    }

    // Step 7: Return successful calculation
    console.log('Calculation successful');
    
    return {
      success: true,
      salaryStructure: salaryStructure,
      employeeRetiralDetails: employeeRetiralDetails,
      errorCode: null,
      errorMessage: null
    };

  } catch (error) {
    console.error('Unexpected error in calculateEmployeeSalary:', error);
    
    return {
      success: false,
      errorCode: 'CALC_ERROR',
      errorMessage: error.message || 'Unexpected error during salary calculation'
    };
  }
};

module.exports = {
  calculateEmployeeSalary: calculateEmployeeSalary
};
