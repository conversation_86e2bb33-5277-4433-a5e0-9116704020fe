/**
 * Salary revision calculation logic for formId 360 ONLY
 * Handles ONLY formId 360 (Salary Revision) with new simplified 6-field input structure
 */

const { ehrTables } = require('../common/tablealias');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

/**
 * Calculate salary revision for formId 360 with new 6-field input structure
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} salaryRevisionRecord - Salary revision record with 6 fields
 * @param {number} loginEmployeeId - Login employee ID
 * @param {string} orgCode - Organization code
 * @returns {Object} - Calculation result
 */
const calculateSalaryRevision = async (organizationDbConnection, salaryRevisionRecord, loginEmployeeId, orgCode) => {
  try {
    console.log('=== CALCULATING SALARY REVISION (FormId 360) ===');
    console.log('Input record:', JSON.stringify(salaryRevisionRecord, null, 2));
    
    // Extract 6-field input structure
    const {
      Employee_Id,
      Employee_Name,
      Salary_Template,
      Previous_Annual_Ctc,
      Revise_By,
      Amount_Or_Percentage
    } = salaryRevisionRecord;

    // Step 1: Calculate new Annual CTC based on Revise_By
    let newAnnualCtc;
    if (Revise_By === 'Amount') {
      newAnnualCtc = Previous_Annual_Ctc + Amount_Or_Percentage;
      console.log(`Calculating by Amount: ${Previous_Annual_Ctc} + ${Amount_Or_Percentage} = ${newAnnualCtc}`);
    } else if (Revise_By === 'Percentage') {
      newAnnualCtc = Previous_Annual_Ctc + (Previous_Annual_Ctc * Amount_Or_Percentage / 100);
      console.log(`Calculating by Percentage: ${Previous_Annual_Ctc} + (${Previous_Annual_Ctc} * ${Amount_Or_Percentage} / 100) = ${newAnnualCtc}`);
    } else {
      return {
        errorCode: 'INVALID_REVISE_BY',
        message: `Invalid Revise_By value: ${Revise_By}. Must be 'Amount' or 'Percentage'`
      };
    }

    // Step 2: Get roundoff settings from organization
    const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
      .where('Organization_Code', orgCode)
      .first();

    // Apply roundoff logic (same as calculateSalary API)
    if (roundOffSettings) {
      const roundOffValue = getRoundOffValue(360, newAnnualCtc, roundOffSettings);
      newAnnualCtc = roundOffValue;
      console.log(`Applied roundoff: ${newAnnualCtc}`);
    }

    // Step 3: Get maxPayslipMonth for calculating Effective_From
    const maxPayslipMonth = await commonLib.func.maxPayslipMonth(
      organizationDbConnection,
      Employee_Id,
      'salary' // salaryType
    );

    // Calculate Effective_From (maxPayslipMonth + 1) in "M,YYYY" format
    let effectiveFrom;
    let payoutMonth;
    
    if (maxPayslipMonth) {
      // Parse maxPayslipMonth format "YYYY-MM-00"
      const [year, month] = maxPayslipMonth.split('-');
      let nextMonth = parseInt(month) + 1;
      let nextYear = parseInt(year);
      
      if (nextMonth > 12) {
        nextMonth = 1;
        nextYear += 1;
      }
      
      effectiveFrom = `${nextMonth},${nextYear}`;
      payoutMonth = effectiveFrom; // Same as Effective_From
    } else {
      // Default to current month + 1 if no payslip history
      const now = new Date();
      let nextMonth = now.getMonth() + 2; // getMonth() is 0-based, so +2 for next month
      let nextYear = now.getFullYear();
      
      if (nextMonth > 12) {
        nextMonth = 1;
        nextYear += 1;
      }
      
      effectiveFrom = `${nextMonth},${nextYear}`;
      payoutMonth = effectiveFrom;
    }

    console.log(`Calculated Effective_From: ${effectiveFrom}, Payout_Month: ${payoutMonth}`);

    // Step 4: Fetch template allowances from database
    const templateAllowances = await organizationDbConnection(ehrTables.templateAllowanceComponents)
      .where('Template_Id', Salary_Template)
      .select('*');

    // Step 5: Fetch template retirals from database
    const templateRetirals = await organizationDbConnection(ehrTables.templateRetiralComponents)
      .where('Template_Id', Salary_Template)
      .select('*');

    // Step 6: Fetch template gross components from database
    const templateGross = await organizationDbConnection(ehrTables.templateGrossComponents)
      .where('Template_Id', Salary_Template)
      .select('*');

    // Step 7: Get provident fund configuration value
    const providentFundConfigData = await organizationDbConnection(ehrTables.orgDetails)
      .select('Provident_Fund_Configuration')
      .first();
    
    const providentFundConfigurationValue = providentFundConfigData?.Provident_Fund_Configuration;

    console.log('Template data fetched successfully');
    console.log(`Template Allowances: ${templateAllowances.length}`);
    console.log(`Template Retirals: ${templateRetirals.length}`);
    console.log(`Template Gross: ${templateGross.length}`);
    console.log(`PF Configuration: ${providentFundConfigurationValue}`);

    // Step 8: Format allowanceDetails for calculateSalary resolver
    const allowanceDetails = JSON.stringify(templateAllowances.map(allowance => ({
      Employee_Salary_Id: null, // Will be set after salary creation
      Allowance_Type_Id: allowance.Allowance_Type_Id,
      Allowance_Type: allowance.Allowance_Type,
      Percentage: allowance.Percentage,
      Amount: allowance.Amount
    })));

    // Step 9: Format retiralDetails for calculateSalary resolver
    const retiralDetails = JSON.stringify(templateRetirals.map(retiral => ({
      Employee_Salary_Id: null, // Will be set after salary creation
      Form_Id: retiral.Form_Id,
      Retirals_Id: retiral.Retirals_Id,
      Retirals_Type: retiral.Retirals_Type,
      Employee_Share_Percentage: retiral.Employee_Share_Percentage,
      Employer_Share_Percentage: retiral.Employer_Share_Percentage
    })));

    // Step 10: Format grossIds for calculateSalary resolver
    const grossIds = templateGross.map(gross => gross.Gross_Id);

    // Step 11: Format salaryDetails for calculateSalary resolver
    const salaryDetails = JSON.stringify({
      Employee_Id: Employee_Id,
      Annual_Ctc: newAnnualCtc,
      Basic_Pay: null, // Will be calculated by calculateSalary
      Effective_From: new Date().toISOString().split('T')[0], // Format: "2025-08-01"
      Salary_Effective_Month: effectiveFrom
    });

    console.log('Prepared parameters for calculateSalary resolver');

    // Step 12: Prepare calculated data for next phase
    const calculatedData = {
      // Original input
      Employee_Id,
      Employee_Name,
      Salary_Template,
      Previous_Annual_Ctc,
      Revise_By,
      Amount_Or_Percentage,

      // Calculated values
      newAnnualCtc,
      effectiveFrom,
      payoutMonth,

      // Parameters for calculateSalary resolver
      allowanceDetails,
      retiralDetails,
      grossIds,
      salaryDetails,
      providentFundConfigurationValue,

      // Template data
      templateAllowances,
      templateRetirals,
      templateGross,

      // Metadata
      calculationType: 'SALARY_REVISION',
      formId: 360
    };

    console.log('Salary revision calculation completed successfully');

    return {
      success: true,
      calculatedData: calculatedData
    };

  } catch (error) {
    console.error('Error in calculateSalaryRevision:', error);
    return {
      errorCode: 'SALARY_REVISION_CALCULATION_ERROR',
      message: error.message || 'An error occurred during salary revision calculation'
    };
  }
};

/**
 * Get round off value using the same logic as calculateSalary API
 * @param {number} formId - Form ID
 * @param {number} value - Value to round off
 * @param {Object} roundOffSettings - Round off settings from database
 * @returns {number} - Rounded value
 */
const getRoundOffValue = (formId, value, roundOffSettings) => {
  try {
    if (!roundOffSettings || !value) {
      return value;
    }

    // Apply roundoff logic based on settings
    const roundOffType = roundOffSettings.Round_Off_Type;
    const roundOffValue = roundOffSettings.Round_Off_Value || 1;

    if (roundOffType === 'Nearest') {
      return Math.round(value / roundOffValue) * roundOffValue;
    } else if (roundOffType === 'Up') {
      return Math.ceil(value / roundOffValue) * roundOffValue;
    } else if (roundOffType === 'Down') {
      return Math.floor(value / roundOffValue) * roundOffValue;
    }

    return value;
  } catch (error) {
    console.error('Error in getRoundOffValue:', error);
    return value;
  }
};

module.exports = {
  calculateSalaryRevision,
  getRoundOffValue
};
