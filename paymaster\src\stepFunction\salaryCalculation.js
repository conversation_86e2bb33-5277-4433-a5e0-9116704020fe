/**
 * PHASE 1: Pure Salary Calculation Function
 * 
 * Responsibility: Calculate salary structure from input parameters
 * - NO database writes
 * - NO side effects
 * - Returns error object instead of throwing
 * 
 * Input: Salary details (formId, employeeId, ctc, allowances, retirals, etc)
 * Output: {success, salaryStructure, employeeRetiralDetails, errorCode?, errorMessage?}
 */

const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { calculateSalary } = require('../roresolvers/salary/calculateSalary');

/**
 * Calculate employee salary based on input parameters and formId
 * @param {Object} organizationDbConnection - Knex database connection
 * @param {Object} salaryInput - Salary input parameters
 * @returns {Promise<Object>} - Calculation result with salary structure or error
 */
const calculateEmployeeSalary = async (organizationDbConnection, salaryInput) => {
  try {
    console.log('=== PHASE 1: CALCULATE SALARY ===');
    console.log(`Employee: ${salaryInput.employeeId}, Form: ${salaryInput.formId}`);

    // Step 1: Validate required input parameters
    if (!salaryInput.employeeId) {
      return {
        success: false,
        errorCode: 'IVE0001',
        errorMessage: 'Missing required field: employeeId'
      };
    }

    if (!salaryInput.annualCtc) {
      return {
        success: false,
        errorCode: 'IVE0002',
        errorMessage: 'Missing required field: annualCtc'
      };
    }

    if (!salaryInput.formId) {
      return {
        success: false,
        errorCode: 'IVE0003',
        errorMessage: 'Missing required field: formId'
      };
    }

    // Step 2: Validate formId is supported
    const supportedFormIds = [206, 207, 360];
    if (!supportedFormIds.includes(parseInt(salaryInput.formId))) {
      return {
        success: false,
        errorCode: 'IVE0004',
        errorMessage: `Unsupported formId: ${salaryInput.formId}. Supported: ${supportedFormIds.join(', ')}`
      };
    }

    // Step 3: Route to formId-specific calculation
    switch (parseInt(salaryInput.formId)) {
      case 206:
        return await calculateSalaryTemplate(organizationDbConnection, salaryInput);
      case 207:
        return await calculateSalaryDetails(organizationDbConnection, salaryInput);
      case 360:
        return await calculateSalaryRevision(organizationDbConnection, salaryInput);
      default:
        return {
          success: false,
          errorCode: 'IVE0004',
          errorMessage: `Invalid formId: ${salaryInput.formId}`
        };
    }

  } catch (error) {
    console.error('Unexpected error in calculateEmployeeSalary:', error);

    return {
      success: false,
      errorCode: 'CALC_ERROR',
      errorMessage: error.message || 'Unexpected error during salary calculation'
    };
  }
};

/**
 * Calculate salary for formId 206 (Salary Template)
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} salaryInput - Salary input parameters
 * @returns {Promise<Object>} - Calculation result
 */
const calculateSalaryTemplate = async (organizationDbConnection, salaryInput) => {
  console.log('Calculating salary for formId 206 (Salary Template)');

  // For formId 206, we need templateId and basic template-based calculation
  if (!salaryInput.templateId) {
    return {
      success: false,
      errorCode: 'IVE0005',
      errorMessage: 'Missing required field for Salary Template: templateId'
    };
  }

  // TODO: Implement template-based calculation logic
  // For now, return a basic structure to prevent blocking
  return {
    success: true,
    salaryStructure: {
      formId: 206,
      templateId: salaryInput.templateId,
      annualCtc: salaryInput.annualCtc,
      monthlyCtc: salaryInput.annualCtc / 12,
      basic: (salaryInput.annualCtc / 12) * 0.4, // 40% basic as default
      total: salaryInput.annualCtc / 12
    },
    employeeRetiralDetails: {
      templateAllowances: [],
      templateRetirals: []
    },
    errorCode: null,
    errorMessage: null
  };
};

/**
 * Calculate salary for formId 207 (Salary Details) - Uses existing calculateSalary resolver
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} salaryInput - Salary input parameters
 * @returns {Promise<Object>} - Calculation result
 */
const calculateSalaryDetails = async (organizationDbConnection, salaryInput) => {
  console.log('Calculating salary for formId 207 (Salary Details)');

  try {
    // Step 1: Prepare parameters for calculateSalary resolver
    const allowanceDetails = salaryInput.allowances
      ? JSON.stringify(salaryInput.allowances)
      : '[]';

    const retiralDetails = salaryInput.retirals
      ? JSON.stringify(salaryInput.retirals)
      : '[]';

    const salaryDetails = {
      Annual_Ctc: salaryInput.annualCtc,
      Salary_Effective_Month: salaryInput.salaryEffectiveMonth || null
    };

    // Step 2: Prepare mock GraphQL context for resolver compatibility
    const mockContext = {
      connection: { OrganizationDb: organizationDbConnection },
      orgdb: organizationDbConnection
    };

    // Step 3: Call calculateSalary resolver (from existing roresolvers)
    console.log('Calling calculateSalary resolver...');

    const calculateResult = await calculateSalary(null, {
      employeeId: salaryInput.employeeId,
      candidateId: salaryInput.candidateId || null,
      allowanceDetails: allowanceDetails,
      retiralDetails: retiralDetails,
      salaryDetails: salaryDetails,
      revisionWithoutArrear: salaryInput.revisionWithoutArrear || false,
      grossIds: salaryInput.grossIds || []
    }, mockContext);

    // Step 4: Check if resolver returned calculation error
    if (calculateResult.errorCode) {
      console.log(`Calculation failed: ${calculateResult.errorCode}`);

      return {
        success: false,
        errorCode: calculateResult.errorCode,
        errorMessage: calculateResult.message || 'Salary calculation failed'
      };
    }

    // Step 5: Extract and parse calculated data
    let salaryStructure = {};
    let employeeRetiralDetails = {};

    try {
      if (calculateResult.salaryStructure) {
        salaryStructure = typeof calculateResult.salaryStructure === 'string'
          ? JSON.parse(calculateResult.salaryStructure)
          : calculateResult.salaryStructure;
      }

      if (calculateResult.employeeRetiralDetails) {
        employeeRetiralDetails = typeof calculateResult.employeeRetiralDetails === 'string'
          ? JSON.parse(calculateResult.employeeRetiralDetails)
          : calculateResult.employeeRetiralDetails;
      }
    } catch (parseError) {
      console.error('Error parsing calculated response:', parseError);

      return {
        success: false,
        errorCode: 'PARSE_ERROR',
        errorMessage: 'Failed to parse salary calculation response'
      };
    }

    // Step 6: Return successful calculation
    console.log('Calculation successful for formId 207');

    return {
      success: true,
      salaryStructure: salaryStructure,
      employeeRetiralDetails: employeeRetiralDetails,
      errorCode: null,
      errorMessage: null
    };

  } catch (error) {
    console.error('Error in calculateSalaryDetails:', error);

    return {
      success: false,
      errorCode: 'CALC_207_ERROR',
      errorMessage: error.message || 'Error calculating salary details'
    };
  }
};

/**
 * Calculate salary for formId 360 (Salary Revision)
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} salaryInput - Salary input parameters
 * @returns {Promise<Object>} - Calculation result
 */
const calculateSalaryRevision = async (organizationDbConnection, salaryInput) => {
  console.log('Calculating salary for formId 360 (Salary Revision)');

  try {
    // For formId 360, we need revisionId and revision-specific fields
    if (!salaryInput.templateId) {
      return {
        success: false,
        errorCode: 'IVE0006',
        errorMessage: 'Missing required field for Salary Revision: templateId'
      };
    }

    if (!salaryInput.salaryEffectiveMonth) {
      return {
        success: false,
        errorCode: 'IVE0007',
        errorMessage: 'Missing required field for Salary Revision: salaryEffectiveMonth'
      };
    }

    // TODO: Implement revision-specific calculation logic
    // For now, return a basic structure to prevent blocking
    return {
      success: true,
      salaryStructure: {
        formId: 360,
        templateId: salaryInput.templateId,
        annualCtc: salaryInput.annualCtc,
        monthlyCtc: salaryInput.annualCtc / 12,
        basic: (salaryInput.annualCtc / 12) * 0.4, // 40% basic as default
        total: salaryInput.annualCtc / 12,
        salaryEffectiveMonth: salaryInput.salaryEffectiveMonth,
        payoutMonth: salaryInput.payoutMonth || null
      },
      employeeRetiralDetails: {
        revisionAllowances: [],
        revisionRetirals: []
      },
      errorCode: null,
      errorMessage: null
    };

  } catch (error) {
    console.error('Error in calculateSalaryRevision:', error);

    return {
      success: false,
      errorCode: 'CALC_360_ERROR',
      errorMessage: error.message || 'Error calculating salary revision'
    };
  }
};

module.exports = {
  calculateEmployeeSalary: calculateEmployeeSalary
};
