/**
 * GraphQL mutation to trigger bulk salary revision import using AWS Step Functions
 * ONLY supports formId 360 (Salary Revision) with new simplified 6-field input structure
 */

const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../common/tablealias');
const AWS = require('aws-sdk');

// Initialize Step Functions client
const stepFunctions = new AWS.StepFunctions({
  region: process.env.AWS_REGION || 'us-east-1'
});

/**
 * Main resolver function to trigger bulk salary revision import
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments containing salaryRevisionRecords array
 * @param {Object} context - Context object containing user info and database connections
 * @returns {Object} - Response with operation status and import tracking details
 */
const triggerSalaryRevisionImport = async (parent, args, context) => {
  let organizationDbConnection;
  
  try {
    console.log('=== TRIGGERING BULK SALARY REVISION IMPORT ===');
    console.log(`Processing ${args.salaryRevisionRecords?.length || 0} records`);

    // Get user info from context
    const {
      logInEmpId: loginEmployeeId,
      orgCode
    } = context;

    // Initialize database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Validate input
    if (!args.salaryRevisionRecords || !Array.isArray(args.salaryRevisionRecords) || args.salaryRevisionRecords.length === 0) {
      throw new Error('salaryRevisionRecords array is required and cannot be empty');
    }

    // Validate each record structure (6 fields only)
    for (let i = 0; i < args.salaryRevisionRecords.length; i++) {
      const record = args.salaryRevisionRecords[i];
      
      // Required fields validation
      if (!record.Employee_Id) {
        throw new Error(`Record ${i + 1}: Employee_Id is required`);
      }
      if (!record.Employee_Name || record.Employee_Name.trim() === '') {
        throw new Error(`Record ${i + 1}: Employee_Name is required`);
      }
      if (!record.Salary_Template) {
        throw new Error(`Record ${i + 1}: Salary_Template (Template_Id) is required`);
      }
      if (!record.Previous_Annual_Ctc || record.Previous_Annual_Ctc <= 0) {
        throw new Error(`Record ${i + 1}: Previous_Annual_Ctc must be a positive number`);
      }
      if (!record.Revise_By || !['Amount', 'Percentage'].includes(record.Revise_By)) {
        throw new Error(`Record ${i + 1}: Revise_By must be either 'Amount' or 'Percentage'`);
      }
      
      // Validate Amount_Or_Percentage based on Revise_By
      if (record.Revise_By === 'Amount') {
        if (!record.Amount_Or_Percentage || record.Amount_Or_Percentage <= 0) {
          throw new Error(`Record ${i + 1}: Amount_Or_Percentage must be a positive number when Revise_By is 'Amount'`);
        }
      } else if (record.Revise_By === 'Percentage') {
        if (!record.Amount_Or_Percentage || record.Amount_Or_Percentage <= 0 || record.Amount_Or_Percentage > 100) {
          throw new Error(`Record ${i + 1}: Amount_Or_Percentage must be between 0 and 100 when Revise_By is 'Percentage'`);
        }
      }
    }

    console.log(`All ${args.salaryRevisionRecords.length} records validated successfully`);

    // Check access rights for formId 360 (Salary Revision)
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      360 // formId for Salary Revision
    );

    if (!checkRights || Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
      throw '_DB0101'; // No add access for salary revision
    }

    // Create import tracking record in salary_import table
    const currentTimestamp = new Date();
    const [salaryImportId] = await organizationDbConnection('salary_import')
      .insert({
        Import_Type: 'Revision',
        Actual_File_Name: `salary_revision_import_${Date.now()}.xlsx`, // Will be updated after file generation
        Error_File_Name: `salary_revision_errors_${Date.now()}.xlsx`, // Will be updated after file generation
        Import_Status: 'In Progress',
        Added_On: currentTimestamp,
        Added_By: loginEmployeeId,
        Total_Records: args.salaryRevisionRecords.length,
        Success_Records: 0,
        Failed_Records: 0,
        Processing_Start_Time: currentTimestamp,
        Organization_Code: orgCode,
        Updated_On: currentTimestamp
      });

    console.log(`Created salary import record with ID: ${salaryImportId}`);

    // Prepare Step Function input
    const stepFunctionInput = {
      salaryImportId: salaryImportId,
      orgCode: orgCode,
      loginEmployeeId: loginEmployeeId,
      salaryRevisionRecords: args.salaryRevisionRecords,
      totalRecords: args.salaryRevisionRecords.length,
      batchSize: 5, // Process 5 records in parallel
      timestamp: currentTimestamp.toISOString()
    };

    // Get Step Function ARN from environment
    const stepFunctionArn = process.env.SALARY_REVISION_IMPORT_STEP_FUNCTION_ARN;
    if (!stepFunctionArn) {
      throw new Error('SALARY_REVISION_IMPORT_STEP_FUNCTION_ARN environment variable is not configured');
    }

    // Start Step Function execution
    const executionParams = {
      stateMachineArn: stepFunctionArn,
      name: `salary-revision-import-${salaryImportId}-${Date.now()}`,
      input: JSON.stringify(stepFunctionInput)
    };

    console.log(`Starting Step Function execution: ${executionParams.name}`);
    
    const execution = await stepFunctions.startExecution(executionParams).promise();
    
    console.log(`Step Function started successfully: ${execution.executionArn}`);

    return {
      errorCode: '',
      message: 'Bulk salary revision import initiated successfully',
      salaryImportId: salaryImportId,
      executionArn: execution.executionArn,
      totalRecords: args.salaryRevisionRecords.length,
      status: 'In Progress'
    };

  } catch (error) {
    console.error('Error in triggerSalaryRevisionImport:', error);
    
    // Handle specific error types
    let errorCode = 'SALARY_REVISION_IMPORT_ERROR';
    let errorMessage = error.message || 'An error occurred while initiating bulk salary revision import';
    
    if (error.code === 'ValidationException') {
      errorCode = 'VALIDATION_ERROR';
    } else if (error.code === 'InvalidParameterValueException') {
      errorCode = 'INVALID_PARAMETER';
    } else if (error.code === 'StateMachineDoesNotExist') {
      errorCode = 'STEP_FUNCTION_NOT_FOUND';
    }

    throw new ApolloError(errorMessage, errorCode);
    
  } finally {
    // Clean up database connections
    if (organizationDbConnection) {
      organizationDbConnection.destroy();
    }
  }
};

module.exports = {
  triggerSalaryRevisionImport
};
