/** Alias name for ehr tables */
module.exports.ehrTables = {
    empJob: 'emp_job',
    empPersonalInfo: 'emp_personal_info',
    designation: 'designation',
    employeeType: 'employee_type',
    attendanceSettings: 'attendance_settings',
    attendanceWorkPlace: 'attendance_work_place',
    attendancePolicy: 'attendance_policy',
    attendancePolicyEligibleWorkPlace: 'attendance_policy_eligible_work_place',
    attendancePolicyAutoApprovalWorkPlace: 'attendance_policy_auto_approval_work_place',
    workSchedule: 'work_schedule',
    leaveTypes: 'leave_types',
    leavetypegrade: 'leavetype_grade',
    empGrade: 'emp_grade',
    empEligbleLeave: 'emp_eligible_leave',
    encashedLeave: 'encashed_leaves',
    customEmployeeGroupEmployees: 'custom_employee_group_employees',
    empLeaves: 'emp_leaves',
    shortTimeOff: 'short_time_off',
    maritalStatus: 'marital_status',
    compensatoryOffBalance:'compensatory_off_balance',
    compensatoryOff: 'compensatory_off',
    holidaySettings: 'holiday_settings',
    holidayAssignment: 'holiday_assignment',
    holidayCustomGroupAssignment: 'holiday_customgroup_assignment',
    holiday: 'holiday',
    resignation: 'emp_resignation',
    leaveClosureConfiguration: 'leave_closure_configuration',
    empExperienceLeave: 'emp_experience_leave',
    advanceSalary: 'advance_salary',
    bonus: 'emp_bonus',
    commission: 'commission',
    deductions: 'deductions',
    empLoan: 'emp_loan',
    reimbursement: 'reimbursement',
    empShift: 'emp_shift',
    performanceAssesment: 'performance_assessment',
    empTravels: 'emp_travels',
    empTransfer: 'emp_transfer',
    empTimesheet: 'emp_timesheet',
    empAttendance: 'emp_attendance',
    ehrForms: 'ehr_forms',
    selfOccupiedTableName: 'self_occupied_property_income',
    rentedIncomeTableName: 'rented_property_income',
    deferredLoan: 'deferred_loan',
    taUserTask: 'ta_user_task',
    taProcessInstance: 'ta_process_instance',
    taProcessInstanceHistory: 'ta_process_instance_history',
    taWorkFlow: 'ta_workflow',
    taWorkFlowVersion: 'ta_workflow_version',
    taEvent: 'ta_event',
    workflows: 'workflows',
    taxDeclarations: 'tax_declaration',
    orgDetails: 'org_details',
    empInbox: 'emp_inbox',
    empAccessRights: 'emp_accessrights',
    candidateRecruitementInfo: 'candidate_recruitment_info',
    hraDeclaration: 'hra_declaration',
    landlordDetails: 'landlord_details',
    contractEmployeeTdsConfiguration: 'contract_employee_tds_configuration',
    contactDetails: 'contact_details',
    location: 'location',
    department: 'department',
    teamMembers: 'em_members',
    timezone: 'timezone',
    salaryPayslip: 'salary_payslip',
    hourlywagesPayslip: 'hourlywages_payslip',
    employeeSalaryDetails: 'employee_salary_details',
    employeeSalaryConfiguration: 'employee_salary_configuration',
    shiftEmpMapping: 'shift_emp_mapping',
    empShiftType: 'emp_shift_type',
    rosterManagementSettings: 'roster_management_settings',
    ehrRoles: 'ehr_roles',
    months: 'months',
    salaryDetails: 'salary_details',
    hourlyWages: 'hourly_wages',
    holidaySpecialwages: 'holiday_specialwages',
    specialwageConfiguration: 'specialwage_configuration',
    timesheetHours: 'timesheet_hours',
    employeeSalaryConfiguration: 'employee_salary_configuration',
    documentTemplateEngine: 'document_template_engine',
    empGeneratedDocuments: 'emp_generated_documents',
    generatedDocumentCustomComponents: 'generated_document_custom_components',
    documentTemplateFields: 'document_template_fields',
    workScheduleWeekoff: 'workschedule_weekoff',
    weekOffDates: "weekoff_dates",
    generatedDocumentSignatureDetails: 'generated_document_signature_details',
    signatureDetails: 'signature_details',
    candidateRecruitmentInfo: 'candidate_recruitment_info',
    atsStatusTable: 'ats_status_table',
    candidatePersonalInfo: 'candidate_personal_info',
    candidateJob: 'candidate_job',
    interviewCandidates: 'interview_candidates',
    candidateContactDetails: 'candidate_contact_details',
    templateFields: 'Template_Fields',
    state: 'state',
    city: 'city',
    country: 'country',
    empDepartmentHistory: 'emp_department_history',
    empDesignationHistory: 'emp_designation_history',
    empLocationHistory: 'emp_location_history',
    jobPost: 'job_post',
    payrollRoundOffSettings: 'payroll_round_off_settings',
    payrollGeneralSettings: 'payroll_general_settings',
    facialRecognition: 'registered_face_user',
    employeeAccreditationDetails: 'employee_accreditation_details',
    accreditationCategoryAndType: 'accreditation_category_and_type',
    serviceProvider: 'service_provider',
    cusEmpGroupFilter: 'custom_employee_group_filters',
    cusGroupAssocitedForms: 'custom_group_associated_forms',
    taUserTaskHistory: 'ta_user_task_history',
    empDependent: 'emp_dependent',
    auditEmpEligibleLeave: 'audit_emp_eligible_leave',
    leaveQuarter: 'leave_quarter',
    leaveJoinQuarter: 'leave_join_quarter',
    empServiceLeave: 'emp_service_leave',
    maternitySlab: 'maternity_slab',
    empBankDetails: 'emp_bankdetails',
    upshotPayoutDetails: 'upshot_payout_details',
    systemLog: 'system_log',
    employeeInfoTimeStampLog: 'employee_info_timestamp_log',
    esicReason: 'esic_reason',
    employeeFullAndFinalSettlement: 'employee_full_and_final_settlement',
    holidaySettings: 'holiday_settings',
    compoffConfiguration: 'compoff_configuration',
    formLevelCoverage: 'form_level_coverage',
    roles: 'roles',
    rolesBasedAccessControl: 'rolesbased_access_control',
    empUser: 'emp_user',
    empInsurance: 'emp_insurancepolicyno',
    employeeLevelScreenshotBlurSettings: 'employee_level_screenshot_blur_settings',
    employeeLevelDeleteScreenshotSettings: 'employee_level_delete_screenshot_settings',
    employeeLevelScreenshotFrequencySettings: 'employee_level_screenshot_frequency_settings',
    employeeLevelWorkHoursGoalsSettings: 'employee_level_work_hours_goals_settings',
    employeeLevelActivityGoalSettings: 'employee_level_activity_goal_settings',
    employeeLevelAppUrlSettings: 'employee_level_app_url_settings',
    employeeLevelIdleTimeSettings: 'employee_level_idle_time_settings',
    employeeLevelInsightsNotificationSettings: 'employee_level_insights_notification_settings',
    employeeLevelFileTransferSettings: "employee_level_file_transfer_settings",
    employeeLevelDlpSettings: 'employee_level_dlp_settings',
    employeeLevelToxicityDetectionSettings: 'employee_level_toxicity_detection_settings',
    employeeMonitorSettings: 'employee_monitor_settings',
    organizationLevelFileTransferSettings: "organization_level_file_transfer_settings",
    organizationLevelDlpSettings: 'organization_level_dlp_settings',
    organizationLevelToxicityDetectionSettings: 'organization_level_toxicity_detection_settings',
    empPassport: "emp_passport",
    empDrivinglicense: "emp_drivinglicense",
    orgLeavePeriodEligibleDays: 'org_leave_period_eligible_days',
    weekdays: 'weekdays',
    externalAPISyncStatus: 'external_api_sync_status',
    externalApiIntegrationLog: 'external_api_integration_log',
    organizationGroup: "organization_group",
    SFWPOrganizationStructure: 'SFWP_Organization_Structure',
    customEmailTemplates: 'custom_email_templates',
    empAttendace: 'emp_attendance',
    empResignation: 'emp_resignation',
    interviews: 'interviews',
    candidateUrl: 'candidate_url',
    jobGrade: 'job_grade',
    businessUnit: 'business_unit',
    employeeAttendanceSummary: 'employee_attendance_summary',
    externalApiSyncDetails: 'external_api_sync_details',
    empTravelRequest:'emp_travel_request',
    leaveTypesPaymentConfigurationSlab:'leave_types_payment_configuration_slab',
    externalApiCredentials: 'external_api_credentials',
    employeeUpdateRequests: 'employee_update_requests',
    employeeSalaryAllowance:'employee_salary_allowance',
    employeeSalaryRetirals:'employee_salary_retirals',
    allowanceType   : 'allowance_type',
    allowances      : 'allowances',
    templateAllowanceComponents : 'template_allowance_components',
    templateRetiralComponents   : 'template_retiral_components',
    employeeOldSalaryRetirals:'employee_old_salary_retirals',
    employeeOldSalaryRetiralsHistory:'employee_old_salary_retirals_history',
    empPrefixConfig :'emp_prefix_config',
    empPrefixSettings : 'emp_prefix_settings',
    unpaidLeaveOverride: 'unpaid_leave_override',
};

module.exports.appManagerTables = {
    forms: 'forms',
    customizationForms: 'customization_forms',
    hrappRegisteruser: 'hrapp_registeruser',
    billingRate: 'billing_rate',
    orgRateChoice: 'org_rate_choice',
    hrappPlanForm: 'hrapp_plan_form',
    hrappPlanDetails: 'hrapp_plan_details'
}