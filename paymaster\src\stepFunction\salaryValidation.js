/**
 * PHASE 2: Salary Validation Function
 * 
 * Responsibility: Validate calculated salary against business rules
 * - NO database writes
 * - NO side effects
 * - Pure validation only
 * 
 * Input: Calculated salary response + original input
 * Output: {valid, errors: [{code, message}, ...]}
 */

/**
 * Validate calculated salary structure against business rules
 * @param {Object} calculationResponse - Response from Phase 1 (calculateEmployeeSalary)
 * @param {Object} salaryInput - Original salary input
 * @returns {Promise<Object>} - Validation result with list of errors
 */
const validateCalculatedSalary = async (calculationResponse, salaryInput) => {
  console.log('=== PHASE 2: VALIDATE SALARY ===');
  
  const errors = [];

  try {
    const { salaryStructure, employeeRetiralDetails } = calculationResponse;

    if (!salaryStructure) {
      return {
        valid: false,
        errors: [{
          code: 'VAL0001',
          message: 'Missing salary structure in calculation response'
        }]
      };
    }

    // Validation 1: CTC Matching
    // Calculate expected monthly CTC
    const annualCtc = salaryInput.annualCtc || 0;
    const monthlyCTC = annualCtc / 12;
    
    // Get calculated total from salary structure
    const calculatedTotal = salaryStructure.total || 0;
    
    // Allow 1 rupee tolerance due to rounding
    const ctcDifference = Math.abs(calculatedTotal - monthlyCTC);
    
    if (ctcDifference > 1) {
      errors.push({
        code: 'VAL0002',
        message: `Calculated salary (${calculatedTotal.toFixed(2)}) does not match monthly CTC (${monthlyCTC.toFixed(2)}). Difference: ${ctcDifference.toFixed(2)}`
      });
    }

    // Validation 2: No negative basic pay
    const basicPay = salaryStructure.basic || 0;
    
    if (basicPay < 0) {
      errors.push({
        code: 'VAL0003',
        message: `Basic pay cannot be negative. Current: ${basicPay.toFixed(2)}`
      });
    }

    // Validation 3: No negative fixed allowance
    const fixedAllowance = salaryStructure.fixedAllowance !== undefined 
      ? salaryStructure.fixedAllowance 
      : 0;
    
    if (fixedAllowance < 0) {
      errors.push({
        code: 'VAL0004',
        message: `Fixed allowance cannot be negative. Current: ${fixedAllowance.toFixed(2)}`
      });
    }

    // Validation 4: No negative final allowance amount
    const finalAllowanceAmount = salaryStructure.finalAllowanceAmount || 0;
    
    if (finalAllowanceAmount < 0) {
      errors.push({
        code: 'VAL0005',
        message: `Final allowance amount cannot be negative. Current: ${finalAllowanceAmount.toFixed(2)}`
      });
    }

    // Validation 5: PF amount bounds (max 50% of monthly CTC)
    const pfEmployerShare = salaryStructure.pfEmployerShareAmount || 0;
    const maxPFAmount = monthlyCTC * 0.5;
    
    if (pfEmployerShare > maxPFAmount) {
      errors.push({
        code: 'VAL0006',
        message: `PF employer share (${pfEmployerShare.toFixed(2)}) exceeds max allowed (${maxPFAmount.toFixed(2)})`
      });
    }

    // Validation 6: Insurance amount bounds (max 5% of monthly CTC)
    const insuranceShare = salaryStructure.insuranceEmployerShareAmount || 0;
    const maxInsuranceAmount = monthlyCTC * 0.05;
    
    if (insuranceShare > maxInsuranceAmount) {
      errors.push({
        code: 'VAL0007',
        message: `Insurance share (${insuranceShare.toFixed(2)}) exceeds max allowed (${maxInsuranceAmount.toFixed(2)})`
      });
    }

    // Validation 7: Gratuity amount bounds (max 10% of monthly CTC)
    const gratuityAmount = salaryStructure.gratuityAmount || 0;
    const maxGratuityAmount = monthlyCTC * 0.1;
    
    if (gratuityAmount > maxGratuityAmount) {
      errors.push({
        code: 'VAL0008',
        message: `Gratuity amount (${gratuityAmount.toFixed(2)}) exceeds max allowed (${maxGratuityAmount.toFixed(2)})`
      });
    }

    // Validation 8: Gross salary should match total
    const grossSalary = salaryStructure.grossSalary || 0;
    const tolerance = 1; // 1 rupee tolerance
    
    if (Math.abs(grossSalary - finalAllowanceAmount) > tolerance) {
      errors.push({
        code: 'VAL0009',
        message: `Gross salary (${grossSalary.toFixed(2)}) does not match final allowance (${finalAllowanceAmount.toFixed(2)})`
      });
    }

    // Validation 9: Check if error code was set in calculation
    if (salaryStructure.errorCode) {
      errors.push({
        code: 'VAL0010',
        message: `Calculation contained error: ${salaryStructure.errorCode}`
      });
    }

    // Validation passed if no errors
    if (errors.length === 0) {
      console.log('All validations passed');
    } else {
      console.log(`Validation failed with ${errors.length} error(s)`);
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };

  } catch (error) {
    console.error('Error in validateCalculatedSalary:', error);
    
    return {
      valid: false,
      errors: [{
        code: 'VAL_ERROR',
        message: error.message || 'Validation check failed'
      }]
    };
  }
};

module.exports = {
  validateCalculatedSalary: validateCalculatedSalary
};
