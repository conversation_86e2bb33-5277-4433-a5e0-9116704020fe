/**
 * Salary revision validation logic for formId 360 ONLY
 * Handles ONLY formId 360 (Salary Revision) with new simplified 6-field input structure
 */

const { ehrTables } = require('../common/tablealias');

/**
 * Validate salary revision calculated data for formId 360
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} calculatedData - Calculated salary revision data
 * @param {number} loginEmployeeId - Login employee ID
 * @param {string} orgCode - Organization code
 * @returns {Object} - Validation result
 */
const validateSalaryRevision = async (organizationDbConnection, calculatedData, loginEmployeeId, orgCode) => {
  try {
    console.log('=== VALIDATING SALARY REVISION (FormId 360) ===');
    console.log('Calculated data:', JSON.stringify(calculatedData, null, 2));
    
    const validationErrors = [];

    // Extract data for validation
    const {
      Employee_Id,
      Employee_Name,
      Salary_Template,
      Previous_Annual_Ctc,
      Revise_By,
      Amount_Or_Percentage,
      newAnnualCtc,
      effectiveFrom,
      payoutMonth,
      templateAllowances,
      templateRetirals,
      templateGross
    } = calculatedData;

    // Validation 1: Check if employee exists
    const employeeExists = await organizationDbConnection(ehrTables.empPersonalInfo)
      .where('Employee_Id', Employee_Id)
      .first();

    if (!employeeExists) {
      validationErrors.push({
        code: 'IVE0010',
        message: `Employee with ID ${Employee_Id} does not exist`
      });
    }

    // Validation 2: Check if salary template exists
    const templateExists = await organizationDbConnection(ehrTables.salaryTemplate)
      .where('Template_Id', Salary_Template)
      .first();

    if (!templateExists) {
      validationErrors.push({
        code: 'IVE0011',
        message: `Salary template with ID ${Salary_Template} does not exist`
      });
    }

    // Validation 3: Validate Previous_Annual_Ctc is positive
    if (!Previous_Annual_Ctc || Previous_Annual_Ctc <= 0) {
      validationErrors.push({
        code: 'IVE0012',
        message: 'Previous_Annual_Ctc must be a positive number'
      });
    }

    // Validation 4: Validate Revise_By enum
    if (!Revise_By || !['Amount', 'Percentage'].includes(Revise_By)) {
      validationErrors.push({
        code: 'IVE0013',
        message: 'Revise_By must be either "Amount" or "Percentage"'
      });
    }

    // Validation 5: Validate Amount_Or_Percentage based on Revise_By
    if (Revise_By === 'Amount') {
      if (!Amount_Or_Percentage || Amount_Or_Percentage <= 0) {
        validationErrors.push({
          code: 'IVE0014',
          message: 'Amount_Or_Percentage must be a positive number when Revise_By is "Amount"'
        });
      }
    } else if (Revise_By === 'Percentage') {
      if (!Amount_Or_Percentage || Amount_Or_Percentage <= 0 || Amount_Or_Percentage > 100) {
        validationErrors.push({
          code: 'IVE0015',
          message: 'Amount_Or_Percentage must be between 0 and 100 when Revise_By is "Percentage"'
        });
      }
    }

    // Validation 6: Validate new Annual CTC is reasonable
    if (!newAnnualCtc || newAnnualCtc <= 0) {
      validationErrors.push({
        code: 'IVE0016',
        message: 'Calculated new Annual CTC must be a positive number'
      });
    }

    // Validation 7: Check if new CTC is not too different from previous (business rule)
    if (newAnnualCtc && Previous_Annual_Ctc) {
      const changePercentage = Math.abs((newAnnualCtc - Previous_Annual_Ctc) / Previous_Annual_Ctc) * 100;
      if (changePercentage > 200) { // More than 200% change
        validationErrors.push({
          code: 'IVE0017',
          message: `New Annual CTC change of ${changePercentage.toFixed(2)}% seems unreasonable. Please verify.`
        });
      }
    }

    // Validation 8: Validate effective date format
    if (!effectiveFrom || !/^\d{1,2},\d{4}$/.test(effectiveFrom)) {
      validationErrors.push({
        code: 'IVE0018',
        message: 'Effective_From must be in "M,YYYY" format'
      });
    }

    // Validation 9: Validate payout month format
    if (!payoutMonth || !/^\d{1,2},\d{4}$/.test(payoutMonth)) {
      validationErrors.push({
        code: 'IVE0019',
        message: 'Payout_Month must be in "M,YYYY" format'
      });
    }

    // Validation 10: Check if template has required components
    if (!templateAllowances || templateAllowances.length === 0) {
      validationErrors.push({
        code: 'IVE0020',
        message: `Salary template ${Salary_Template} has no allowance components defined`
      });
    }

    // Validation 11: Validate employee name is not empty
    if (!Employee_Name || Employee_Name.trim() === '') {
      validationErrors.push({
        code: 'IVE0021',
        message: 'Employee_Name cannot be empty'
      });
    }

    // Validation 12: Check for duplicate salary revision in same effective month
    const existingRevision = await organizationDbConnection(ehrTables.salaryRevisionDetails)
      .where('Employee_Id', Employee_Id)
      .where('Salary_Effective_Month', effectiveFrom)
      .first();

    if (existingRevision) {
      validationErrors.push({
        code: 'IVE0022',
        message: `Employee ${Employee_Id} already has a salary revision for ${effectiveFrom}`
      });
    }

    console.log(`Validation completed. Found ${validationErrors.length} errors.`);

    if (validationErrors.length > 0) {
      return {
        success: false,
        errorCode: 'VALIDATION_FAILED',
        message: `Salary revision validation failed with ${validationErrors.length} errors`,
        validationErrors: validationErrors
      };
    }

    // All validations passed
    return {
      success: true,
      validatedData: calculatedData,
      message: 'Salary revision validation completed successfully'
    };

  } catch (error) {
    console.error('Error in validateSalaryRevision:', error);
    return {
      success: false,
      errorCode: 'SALARY_REVISION_VALIDATION_ERROR',
      message: error.message || 'An error occurred during salary revision validation'
    };
  }
};

module.exports = {
  validateSalaryRevision
};
