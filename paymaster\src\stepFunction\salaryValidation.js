/**
 * PHASE 2: Salary Validation Function
 * 
 * Responsibility: Validate calculated salary against business rules
 * - NO database writes
 * - NO side effects
 * - Pure validation only
 * 
 * Input: Calculated salary response + original input
 * Output: {valid, errors: [{code, message}, ...]}
 */

/**
 * Validate calculated salary structure against business rules
 * @param {Object} calculationResponse - Response from Phase 1 (calculateEmployeeSalary)
 * @param {Object} salaryInput - Original salary input
 * @returns {Promise<Object>} - Validation result with list of errors
 */
const validateCalculatedSalary = async (calculationResponse, salaryInput) => {
  console.log('=== PHASE 2: VALIDATE SALARY ===');
  
  const errors = [];

  try {
    const { salaryStructure, employeeRetiralDetails } = calculationResponse;

    if (!salaryStructure) {
      return {
        valid: false,
        errors: [{
          code: 'VAL0001',
          message: 'Missing salary structure in calculation response'
        }]
      };
    }

    // Validation 1: CTC Matching
    // Calculate expected monthly CTC
    const annualCtc = salaryInput.annualCtc || 0;
    const monthlyCTC = annualCtc / 12;
    
    // Get calculated total from salary structure
    const calculatedTotal = salaryStructure.total || 0;
    
    // Allow 1 rupee tolerance due to rounding
    const ctcDifference = Math.abs(calculatedTotal - monthlyCTC);
    
    if (ctcDifference > 1) {
      errors.push({
        code: 'VAL0002',
        message: `Calculated salary (${calculatedTotal.toFixed(2)}) does not match monthly CTC (${monthlyCTC.toFixed(2)}). Difference: ${ctcDifference.toFixed(2)}`
      });
    }

    // Validation 2: No negative basic pay
    const basicPay = salaryStructure.basic || 0;
    
    if (basicPay < 0) {
      errors.push({
        code: 'VAL0003',
        message: `Basic pay cannot be negative. Current: ${basicPay.toFixed(2)}`
      });
    }

    // Validation 3: No negative fixed allowance
    const fixedAllowance = salaryStructure.fixedAllowance !== undefined 
      ? salaryStructure.fixedAllowance 
      : 0;
    
    if (fixedAllowance < 0) {
      errors.push({
        code: 'VAL0004',
        message: `Fixed allowance cannot be negative. Current: ${fixedAllowance.toFixed(2)}`
      });
    }

    // Validation 4: No negative final allowance amount
    const finalAllowanceAmount = salaryStructure.finalAllowanceAmount || 0;
    
    if (finalAllowanceAmount < 0) {
      errors.push({
        code: 'VAL0005',
        message: `Final allowance amount cannot be negative. Current: ${finalAllowanceAmount.toFixed(2)}`
      });
    }

    // Validation 5: PF amount bounds (max 50% of monthly CTC)
    const pfEmployerShare = salaryStructure.pfEmployerShareAmount || 0;
    const maxPFAmount = monthlyCTC * 0.5;
    
    if (pfEmployerShare > maxPFAmount) {
      errors.push({
        code: 'VAL0006',
        message: `PF employer share (${pfEmployerShare.toFixed(2)}) exceeds max allowed (${maxPFAmount.toFixed(2)})`
      });
    }

    // Validation 6: Insurance amount bounds (max 5% of monthly CTC)
    const insuranceShare = salaryStructure.insuranceEmployerShareAmount || 0;
    const maxInsuranceAmount = monthlyCTC * 0.05;
    
    if (insuranceShare > maxInsuranceAmount) {
      errors.push({
        code: 'VAL0007',
        message: `Insurance share (${insuranceShare.toFixed(2)}) exceeds max allowed (${maxInsuranceAmount.toFixed(2)})`
      });
    }

    // Validation 7: Gratuity amount bounds (max 10% of monthly CTC)
    const gratuityAmount = salaryStructure.gratuityAmount || 0;
    const maxGratuityAmount = monthlyCTC * 0.1;
    
    if (gratuityAmount > maxGratuityAmount) {
      errors.push({
        code: 'VAL0008',
        message: `Gratuity amount (${gratuityAmount.toFixed(2)}) exceeds max allowed (${maxGratuityAmount.toFixed(2)})`
      });
    }

    // Validation 8: Gross salary should match total
    const grossSalary = salaryStructure.grossSalary || 0;
    const tolerance = 1; // 1 rupee tolerance
    
    if (Math.abs(grossSalary - finalAllowanceAmount) > tolerance) {
      errors.push({
        code: 'VAL0009',
        message: `Gross salary (${grossSalary.toFixed(2)}) does not match final allowance (${finalAllowanceAmount.toFixed(2)})`
      });
    }

    // Validation 9: Check if error code was set in calculation
    if (salaryStructure.errorCode) {
      errors.push({
        code: 'VAL0010',
        message: `Calculation contained error: ${salaryStructure.errorCode}`
      });
    }

    // FormId-specific validations
    const formIdValidationErrors = await validateByFormId(salaryInput.formId, salaryStructure, employeeRetiralDetails, salaryInput);
    errors.push(...formIdValidationErrors);

    // Validation passed if no errors
    if (errors.length === 0) {
      console.log('All validations passed');
    } else {
      console.log(`Validation failed with ${errors.length} error(s)`);
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };

  } catch (error) {
    console.error('Error in validateCalculatedSalary:', error);
    
    return {
      valid: false,
      errors: [{
        code: 'VAL_ERROR',
        message: error.message || 'Validation check failed'
      }]
    };
  }
};

/**
 * FormId-specific validation rules
 * @param {number} formId - Form ID (206, 207, 360)
 * @param {Object} salaryStructure - Calculated salary structure
 * @param {Object} employeeRetiralDetails - Employee retiral details
 * @param {Object} salaryInput - Original salary input
 * @returns {Promise<Array>} - Array of validation errors
 */
const validateByFormId = async (formId, salaryStructure, employeeRetiralDetails, salaryInput) => {
  const errors = [];
  const formIdInt = parseInt(formId);

  try {
    switch (formIdInt) {
      case 206:
        // FormId 206: Salary Template validations
        errors.push(...await validateSalaryTemplate(salaryStructure, employeeRetiralDetails, salaryInput));
        break;

      case 207:
        // FormId 207: Salary Details validations
        errors.push(...await validateSalaryDetails(salaryStructure, employeeRetiralDetails, salaryInput));
        break;

      case 360:
        // FormId 360: Salary Revision validations
        errors.push(...await validateSalaryRevision(salaryStructure, employeeRetiralDetails, salaryInput));
        break;

      default:
        errors.push({
          code: 'VAL0011',
          message: `Unsupported formId for validation: ${formId}`
        });
    }
  } catch (error) {
    console.error(`Error in formId ${formId} validation:`, error);
    errors.push({
      code: 'VAL_FORMID_ERROR',
      message: `FormId ${formId} validation failed: ${error.message}`
    });
  }

  return errors;
};

/**
 * Validate Salary Template (FormId 206)
 */
const validateSalaryTemplate = async (salaryStructure, employeeRetiralDetails, salaryInput) => {
  const errors = [];

  // Template-specific validations
  if (!salaryInput.templateId) {
    errors.push({
      code: 'VAL0206_001',
      message: 'Template ID is required for salary template'
    });
  }

  // Validate template allowances structure
  if (employeeRetiralDetails && employeeRetiralDetails.templateAllowances) {
    const templateAllowances = employeeRetiralDetails.templateAllowances;

    for (let i = 0; i < templateAllowances.length; i++) {
      const allowance = templateAllowances[i];

      if (!allowance.Allowance_Type_Id) {
        errors.push({
          code: 'VAL0206_002',
          message: `Template allowance ${i + 1}: Missing Allowance_Type_Id`
        });
      }

      if (allowance.Amount !== null && allowance.Amount < 0) {
        errors.push({
          code: 'VAL0206_003',
          message: `Template allowance ${i + 1}: Amount cannot be negative`
        });
      }
    }
  }

  return errors;
};

/**
 * Validate Salary Details (FormId 207)
 */
const validateSalaryDetails = async (salaryStructure, employeeRetiralDetails, salaryInput) => {
  const errors = [];

  // Salary details specific validations
  if (employeeRetiralDetails && employeeRetiralDetails.employeeSalaryAllowance) {
    const allowances = employeeRetiralDetails.employeeSalaryAllowance;

    for (let i = 0; i < allowances.length; i++) {
      const allowance = allowances[i];

      if (!allowance.Allowance_Type_Id) {
        errors.push({
          code: 'VAL0207_001',
          message: `Salary allowance ${i + 1}: Missing Allowance_Type_Id`
        });
      }

      if (allowance.Amount !== null && allowance.Amount < 0) {
        errors.push({
          code: 'VAL0207_002',
          message: `Salary allowance ${i + 1}: Amount cannot be negative`
        });
      }
    }
  }

  // Validate retirals
  if (employeeRetiralDetails && employeeRetiralDetails.employeeSalaryRetirals) {
    const retirals = employeeRetiralDetails.employeeSalaryRetirals;

    for (let i = 0; i < retirals.length; i++) {
      const retiral = retirals[i];

      if (retiral.Employee_Share_Amount !== null && retiral.Employee_Share_Amount < 0) {
        errors.push({
          code: 'VAL0207_003',
          message: `Salary retiral ${i + 1}: Employee share amount cannot be negative`
        });
      }

      if (retiral.Employer_Share_Amount !== null && retiral.Employer_Share_Amount < 0) {
        errors.push({
          code: 'VAL0207_004',
          message: `Salary retiral ${i + 1}: Employer share amount cannot be negative`
        });
      }
    }
  }

  return errors;
};

/**
 * Validate Salary Revision (FormId 360)
 */
const validateSalaryRevision = async (salaryStructure, employeeRetiralDetails, salaryInput) => {
  const errors = [];

  // Revision-specific validations
  if (!salaryInput.templateId) {
    errors.push({
      code: 'VAL0360_001',
      message: 'Template ID is required for salary revision'
    });
  }

  if (!salaryInput.salaryEffectiveMonth) {
    errors.push({
      code: 'VAL0360_002',
      message: 'Salary effective month is required for salary revision'
    });
  }

  // Validate revision type
  const validRevisionTypes = ['Increment', 'Decrement', 'Promotion', 'Transfer', 'Other'];
  if (salaryInput.revisionType && !validRevisionTypes.includes(salaryInput.revisionType)) {
    errors.push({
      code: 'VAL0360_003',
      message: `Invalid revision type: ${salaryInput.revisionType}. Valid types: ${validRevisionTypes.join(', ')}`
    });
  }

  // Validate previous CTC vs current CTC
  if (salaryInput.previousCtc && salaryInput.annualCtc) {
    const previousCtc = parseFloat(salaryInput.previousCtc);
    const currentCtc = parseFloat(salaryInput.annualCtc);

    if (salaryInput.revisionType === 'Increment' && currentCtc <= previousCtc) {
      errors.push({
        code: 'VAL0360_004',
        message: `For increment revision, current CTC (${currentCtc}) must be greater than previous CTC (${previousCtc})`
      });
    }

    if (salaryInput.revisionType === 'Decrement' && currentCtc >= previousCtc) {
      errors.push({
        code: 'VAL0360_005',
        message: `For decrement revision, current CTC (${currentCtc}) must be less than previous CTC (${previousCtc})`
      });
    }
  }

  // Validate revision allowances
  if (employeeRetiralDetails && employeeRetiralDetails.revisionAllowances) {
    const revisionAllowances = employeeRetiralDetails.revisionAllowances;

    for (let i = 0; i < revisionAllowances.length; i++) {
      const allowance = revisionAllowances[i];

      if (!allowance.Allowance_Type_Id) {
        errors.push({
          code: 'VAL0360_006',
          message: `Revision allowance ${i + 1}: Missing Allowance_Type_Id`
        });
      }

      if (allowance.Amount !== null && allowance.Amount < 0) {
        errors.push({
          code: 'VAL0360_007',
          message: `Revision allowance ${i + 1}: Amount cannot be negative`
        });
      }
    }
  }

  // Validate revision retirals
  if (employeeRetiralDetails && employeeRetiralDetails.revisionRetirals) {
    const revisionRetirals = employeeRetiralDetails.revisionRetirals;

    for (let i = 0; i < revisionRetirals.length; i++) {
      const retiral = revisionRetirals[i];

      if (retiral.Employee_Share_Amount !== null && retiral.Employee_Share_Amount < 0) {
        errors.push({
          code: 'VAL0360_008',
          message: `Revision retiral ${i + 1}: Employee share amount cannot be negative`
        });
      }

      if (retiral.Employer_Share_Amount !== null && retiral.Employer_Share_Amount < 0) {
        errors.push({
          code: 'VAL0360_009',
          message: `Revision retiral ${i + 1}: Employer share amount cannot be negative`
        });
      }
    }
  }

  return errors;
};

module.exports = {
  validateCalculatedSalary: validateCalculatedSalary
};
