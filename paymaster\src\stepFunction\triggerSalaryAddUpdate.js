/**
 * GraphQL resolver to trigger bulk salary add/update operations via Step Function
 * This resolver initiates a Step Function execution for processing multiple salary records
 */

const { ApolloError } = require('apollo-server-lambda');
const AWS = require('aws-sdk');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ehrTables } = require('../common/tablealias');
const {
  generateImportId,
  createImportTracking,
  updateImportTracking,
  logImportError
} = require('../common/importUtils');

/**
 * Trigger bulk salary add/update operations via Step Function
 * @param {Object} parent - Parent object (unused in GraphQL context)
 * @param {Object} args - Arguments containing salaryRecords array
 * @param {Array} args.salaryRecords - Array of salary records to process
 * @param {Object} context - Context object containing user info and database connection
 * @returns {Object} - Response with import tracking ID and execution ARN
 */
const triggerSalaryAddUpdate = async (parent, args, context) => {
  let organizationDbConnection;

  try {
    console.log('=== TRIGGERING BULK SALARY ADD/UPDATE STEP FUNCTION ===');

    const employeeId = context.logInEmpId;
    const userIp = context.userIp;
    const orgCode = context.orgCode;

    // Validate employee ID
    if (!employeeId) {
      throw new Error('Employee_Id is required but not found in context');
    }

    // Create database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Validate input array
    if (!args.salaryRecords || !Array.isArray(args.salaryRecords) || args.salaryRecords.length === 0) {
      throw 'PST0042'; // No salary records provided
    }

    // Validate max batch size (to prevent resource exhaustion)
    const MAX_BATCH_SIZE = 1000;
    if (args.salaryRecords.length > MAX_BATCH_SIZE) {
      throw new Error(`Batch size exceeds maximum limit of ${MAX_BATCH_SIZE} records`);
    }

    // Get first record's formId for access check (all records should have same formId)
    const accessFormId = args.salaryRecords[0]?.formId;
    
    if (!accessFormId) {
      throw new Error('formId is required in all salary records');
    }

    // Check access rights for bulk operation
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      null,
      '',
      'UI',
      false,
      accessFormId
    );

    if (!checkRights || Object.keys(checkRights).length === 0) {
      throw '_DB0102'; // No access
    }

    // Determine if any record is an update or add operation
    const hasUpdateOperation = args.salaryRecords.some(r => r.isEditMode === true);
    const hasAddOperation = args.salaryRecords.some(r => r.isEditMode !== true);

    // Verify user has appropriate access
    if (hasUpdateOperation && checkRights.Role_Update !== 1) {
      throw '_DB0102'; // No update access
    }

    if (hasAddOperation && checkRights.Role_Add !== 1) {
      throw '_DB0101'; // No add access
    }

    // Generate unique import ID
    const importId = generateImportId();
    const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

    console.log(`Import initiated - ID: ${importId}, Total records: ${args.salaryRecords.length}, Org: ${orgCode}, User: ${employeeId}`);

    // Create import tracking record
    await createImportTracking(
      organizationDbConnection,
      importId,
      orgCode,
      employeeId,
      args.salaryRecords.length
    );

    // Prepare Step Function input with metadata
    const stepFunctionInput = {
      importId: importId,
      salaryRecords: args.salaryRecords.map((record, index) => ({
        ...record,
        recordIndex: index,
        sessionId: employeeId,
        userIp: userIp || 'API',
        orgCode: orgCode,
        importId: importId
      })),
      sessionId: employeeId,
      userIp: userIp,
      orgCode: orgCode
    };

    console.log(`Prepared Step Function input with ${args.salaryRecords.length} records`);

    // Initialize Step Functions client
    const stepFunctionsClient = new AWS.StepFunctions({
      region: process.env.region
    });

    // Invoke Step Function
    const params = {
      stateMachineArn: process.env.processSalaryAddUpdateFunction,
      input: JSON.stringify(stepFunctionInput),
      name: `${importId}-${Date.now()}`  // Unique execution name
    };

    console.log(`Invoking Step Function: ${process.env.processSalaryAddUpdateFunction}`);
    
    const response = await stepFunctionsClient.startExecution(params).promise();

    console.log(`Step Function execution started - ARN: ${response.executionArn}`);

    // Log system activity
    const systemLogParam = {
      userIp: userIp,
      employeeId: employeeId,
      organizationDbConnection: organizationDbConnection,
      message: `Bulk salary add/update initiated: ${args.salaryRecords.length} records (Salary ${hasAddOperation ? 'Add' : ''}${hasAddOperation && hasUpdateOperation ? ' & ' : ''}${hasUpdateOperation ? 'Update' : ''}), Import ID: ${importId}`,
      formId: accessFormId,
      formName: getFormName(accessFormId),
      trackingColumn: 'Import_Id',
      uniqueId: importId
    };
    
    await commonLib.func.createSystemLogActivities(systemLogParam);

    return {
      success: true,
      importId: importId,
      executionArn: response.executionArn,
      totalRecords: args.salaryRecords.length,
      message: `Bulk salary add/update initiated with ${args.salaryRecords.length} records. Import ID: ${importId}`
    };

  } catch (error) {
    console.error('Error triggering bulk salary add/update:', error);

    // Handle specific error codes
    const errorCode = typeof error === 'string' ? error : 'PST0043';
    const errResult = commonLib.func.getError(errorCode, 'PST0043');

    // Customize error messages
    let errorMessage = errResult.message;
    
    if (errorCode === 'PST0042') {
      errorMessage = 'No salary records provided for bulk operation';
    } else if (errorCode === '_DB0101') {
      errorMessage = 'You do not have permission to add salary records';
    } else if (errorCode === '_DB0102') {
      errorMessage = 'You do not have permission to perform this operation';
    } else if (error.message && error.message.includes('Batch size')) {
      errorMessage = error.message;
    } else if (error.message && error.message.includes('formId')) {
      errorMessage = error.message;
    }

    throw new ApolloError(errorMessage, errorCode);

  } finally {
    // Always destroy database connection
    if (organizationDbConnection) {
      try {
        organizationDbConnection.destroy();
      } catch (destroyError) {
        console.warn('Warning: Error destroying database connection:', destroyError.message);
      }
    }
  }
};

/**
 * Get form name based on formId
 * @param {number} formId - Form ID
 * @returns {string} - Form name
 */
function getFormName(formId) {
  const formMap = {
    206: 'Salary Template',
    207: 'Salary Details',
    360: 'Salary Revision'
  };
  return formMap[formId] || 'Salary Form';
}

// Export resolver
const resolvers = {
  Mutation: {
    triggerSalaryAddUpdate
  }
};

exports.resolvers = resolvers;
module.exports.triggerSalaryAddUpdate = triggerSalaryAddUpdate;
