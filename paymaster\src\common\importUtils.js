/**
 * Utility functions for salary import/bulk operations
 */

const { v4: uuidv4 } = require('uuid');
const { ehrTables } = require('./tablealias');

/**
 * Generate UUID v4 for import tracking
 * @returns {string} - UUID v4 string
 */
function generateImportId() {
  return uuidv4();
}

/**
 * Log import error to database
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} errorDetails - Error details to log
 * @param {string} errorDetails.importId - Import batch ID
 * @param {number} errorDetails.recordIndex - Position in batch
 * @param {number} errorDetails.formId - Form ID (206/207/360)
 * @param {string} errorDetails.action - 'add' or 'update'
 * @param {number} errorDetails.employeeId - Employee ID (nullable)
 * @param {number} errorDetails.templateId - Template ID (nullable)
 * @param {number} errorDetails.revisionId - Revision ID (nullable)
 * @param {string} errorDetails.errorCode - Error code
 * @param {string} errorDetails.errorMessage - Error message
 * @param {Object} errorDetails.failedInput - Original input payload
 * @param {string} errorDetails.stackTrace - Stack trace (optional)
 * @returns {Promise<void>}
 */
async function logImportError(organizationDbConnection, errorDetails) {
  try {
    const {
      importId,
      recordIndex,
      formId,
      action,
      employeeId,
      templateId,
      revisionId,
      errorCode,
      errorMessage,
      failedInput,
      stackTrace
    } = errorDetails;

    await organizationDbConnection(ehrTables.salaryImportErrors).insert({
      Import_Id: importId,
      Record_Index: recordIndex,
      Form_Id: formId,
      Action: action,
      Employee_Id: employeeId || null,
      Template_Id: templateId || null,
      Revision_Id: revisionId || null,
      Error_Code: errorCode,
      Error_Message: errorMessage,
      Failed_Input: failedInput ? JSON.stringify(failedInput) : null,
      Stack_Trace: stackTrace || null,
      Created_On: new Date()
    });

    console.log(`Import error logged for record ${recordIndex}, importId: ${importId}, errorCode: ${errorCode}`);
  } catch (error) {
    console.error('Error logging import error to database:', error);
    // Do not throw - we don't want logging failures to stop processing
  }
}

/**
 * Update import tracking record with completion status
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} importId - Import ID
 * @param {number} successCount - Number of successful records
 * @param {number} failureCount - Number of failed records
 * @param {string} status - Final status ('Completed' or 'Failed')
 * @returns {Promise<void>}
 */
async function updateImportTracking(organizationDbConnection, importId, successCount, failureCount, status = 'Completed') {
  try {
    const moment = require('moment-timezone');
    const startTime = await organizationDbConnection(ehrTables.salaryImportTracking)
      .select('Processing_Start_Time')
      .where('Import_Id', importId)
      .first();

    const endTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const startTimeObj = startTime ? moment(startTime.Processing_Start_Time) : moment();
    const durationSeconds = moment(endTime).diff(startTimeObj, 'seconds');

    await organizationDbConnection(ehrTables.salaryImportTracking)
      .where('Import_Id', importId)
      .update({
        Status: status,
        Successful_Records: successCount,
        Failed_Records: failureCount,
        Processing_End_Time: endTime,
        Duration_Seconds: durationSeconds,
        Updated_On: endTime
      });

    console.log(`Import tracking updated for ${importId}: ${successCount} success, ${failureCount} failures, status: ${status}, duration: ${durationSeconds}s`);
  } catch (error) {
    console.error('Error updating import tracking:', error);
    throw error;
  }
}

/**
 * Create initial import tracking record
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} importId - Unique import ID
 * @param {string} organizationCode - Organization code
 * @param {number} initiatedBy - Employee ID of user initiating import
 * @param {number} totalRecords - Total number of records in batch
 * @returns {Promise<void>}
 */
async function createImportTracking(organizationDbConnection, importId, organizationCode, initiatedBy, totalRecords) {
  try {
    const moment = require('moment-timezone');
    const now = moment().utc().format('YYYY-MM-DD HH:mm:ss');

    await organizationDbConnection(ehrTables.salaryImportTracking).insert({
      Import_Id: importId,
      Organization_Code: organizationCode,
      Initiated_By: initiatedBy,
      Initiated_On: now,
      Status: 'In Progress',
      Total_Records: totalRecords,
      Successful_Records: 0,
      Failed_Records: 0,
      Processing_Start_Time: now,
      Created_On: now,
      Updated_On: now
    });

    console.log(`Import tracking created for ${importId}, total records: ${totalRecords}`);
  } catch (error) {
    console.error('Error creating import tracking:', error);
    throw error;
  }
}

/**
 * Get form name for error logging
 * @param {number} formId - Form ID
 * @returns {string} - Form name
 */
function getFormNameByFormId(formId) {
  const formMap = {
    206: 'Salary Template',
    207: 'Salary Details',
    360: 'Salary Revision'
  };
  return formMap[formId] || 'Unknown Form';
}

/**
 * Finalize batch import by calculating success/failure counts and updating tracking
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} importId - Import ID
 * @returns {Promise<Object>} - Finalization result with counts
 */
async function finalizeBatchImport(organizationDbConnection, importId) {
  try {
    console.log(`=== FINALIZING BATCH IMPORT: ${importId} ===`);

    // Get counts from salary_import_employees table
    const statusCounts = await organizationDbConnection('salary_import_employees')
      .select('Processing_Status')
      .count('* as count')
      .where('Import_Id', importId)
      .groupBy('Processing_Status');

    let successCount = 0;
    let failureCount = 0;
    let pendingCount = 0;
    let totalCount = 0;

    statusCounts.forEach(row => {
      const count = parseInt(row.count);
      totalCount += count;

      switch (row.Processing_Status) {
        case 'SUCCESS':
          successCount = count;
          break;
        case 'FAILED':
          failureCount = count;
          break;
        case 'PENDING':
        case 'CALCULATED':
        case 'VALIDATED':
          pendingCount = count;
          break;
      }
    });

    console.log(`Import ${importId} final counts: ${successCount} success, ${failureCount} failed, ${pendingCount} pending, ${totalCount} total`);

    // Determine final status
    let finalStatus;
    if (pendingCount > 0) {
      finalStatus = 'Partially Completed'; // Some records still processing
    } else if (failureCount === 0) {
      finalStatus = 'Completed'; // All successful
    } else if (successCount === 0) {
      finalStatus = 'Failed'; // All failed
    } else {
      finalStatus = 'Completed with Errors'; // Mixed results
    }

    // Update import tracking with final counts
    await updateImportTracking(organizationDbConnection, importId, successCount, failureCount, finalStatus);

    const result = {
      importId: importId,
      totalRecords: totalCount,
      successCount: successCount,
      failureCount: failureCount,
      pendingCount: pendingCount,
      finalStatus: finalStatus,
      successRate: totalCount > 0 ? ((successCount / totalCount) * 100).toFixed(2) : '0.00'
    };

    console.log(`Batch import finalized:`, result);
    return result;

  } catch (error) {
    console.error('Error finalizing batch import:', error);

    // Try to update tracking with error status
    try {
      await updateImportTracking(organizationDbConnection, importId, 0, 0, 'System Error');
    } catch (updateError) {
      console.error('Failed to update tracking with error status:', updateError);
    }

    throw error;
  }
}

/**
 * Sanitize input for logging (remove sensitive data if needed)
 * @param {Object} input - Input object to sanitize
 * @returns {Object} - Sanitized copy of input
 */
function sanitizeInputForLogging(input) {
  if (!input) return null;

  // Create shallow copy and remove any sensitive nested objects if needed
  const copy = { ...input };

  // Keep the essential info for debugging
  return copy;
}

module.exports = {
  generateImportId,
  logImportError,
  updateImportTracking,
  createImportTracking,
  finalizeBatchImport,
  getFormNameByFormId,
  sanitizeInputForLogging
};
