/**
 * Utility functions for salary import/bulk operations
 */

const { v4: uuidv4 } = require('uuid');
const { ehrTables } = require('./tablealias');

/**
 * Generate UUID v4 for import tracking
 * @returns {string} - UUID v4 string
 */
function generateImportId() {
  return uuidv4();
}

/**
 * Log import error to database
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} errorDetails - Error details to log
 * @param {string} errorDetails.importId - Import batch ID
 * @param {number} errorDetails.recordIndex - Position in batch
 * @param {number} errorDetails.formId - Form ID (206/207/360)
 * @param {string} errorDetails.action - 'add' or 'update'
 * @param {number} errorDetails.employeeId - Employee ID (nullable)
 * @param {number} errorDetails.templateId - Template ID (nullable)
 * @param {number} errorDetails.revisionId - Revision ID (nullable)
 * @param {string} errorDetails.errorCode - Error code
 * @param {string} errorDetails.errorMessage - Error message
 * @param {Object} errorDetails.failedInput - Original input payload
 * @param {string} errorDetails.stackTrace - Stack trace (optional)
 * @returns {Promise<void>}
 */
async function logImportError(organizationDbConnection, errorDetails) {
  try {
    const {
      importId,
      recordIndex,
      formId,
      action,
      employeeId,
      templateId,
      revisionId,
      errorCode,
      errorMessage,
      failedInput,
      stackTrace
    } = errorDetails;

    await organizationDbConnection(ehrTables.salaryImportErrors).insert({
      Import_Id: importId,
      Record_Index: recordIndex,
      Form_Id: formId,
      Action: action,
      Employee_Id: employeeId || null,
      Template_Id: templateId || null,
      Revision_Id: revisionId || null,
      Error_Code: errorCode,
      Error_Message: errorMessage,
      Failed_Input: failedInput ? JSON.stringify(failedInput) : null,
      Stack_Trace: stackTrace || null,
      Created_On: new Date()
    });

    console.log(`Import error logged for record ${recordIndex}, importId: ${importId}, errorCode: ${errorCode}`);
  } catch (error) {
    console.error('Error logging import error to database:', error);
    // Do not throw - we don't want logging failures to stop processing
  }
}

/**
 * Update import tracking record with completion status
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} importId - Import ID
 * @param {number} successCount - Number of successful records
 * @param {number} failureCount - Number of failed records
 * @param {string} status - Final status ('Completed' or 'Failed')
 * @returns {Promise<void>}
 */
async function updateImportTracking(organizationDbConnection, importId, successCount, failureCount, status = 'Completed') {
  try {
    const moment = require('moment-timezone');
    const startTime = await organizationDbConnection(ehrTables.salaryImportTracking)
      .select('Processing_Start_Time')
      .where('Import_Id', importId)
      .first();

    const endTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const startTimeObj = startTime ? moment(startTime.Processing_Start_Time) : moment();
    const durationSeconds = moment(endTime).diff(startTimeObj, 'seconds');

    await organizationDbConnection(ehrTables.salaryImportTracking)
      .where('Import_Id', importId)
      .update({
        Status: status,
        Successful_Records: successCount,
        Failed_Records: failureCount,
        Processing_End_Time: endTime,
        Duration_Seconds: durationSeconds,
        Updated_On: endTime
      });

    console.log(`Import tracking updated for ${importId}: ${successCount} success, ${failureCount} failures, status: ${status}, duration: ${durationSeconds}s`);
  } catch (error) {
    console.error('Error updating import tracking:', error);
    throw error;
  }
}

/**
 * Create initial import tracking record
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} importId - Unique import ID
 * @param {string} organizationCode - Organization code
 * @param {number} initiatedBy - Employee ID of user initiating import
 * @param {number} totalRecords - Total number of records in batch
 * @returns {Promise<void>}
 */
async function createImportTracking(organizationDbConnection, importId, organizationCode, initiatedBy, totalRecords) {
  try {
    const moment = require('moment-timezone');
    const now = moment().utc().format('YYYY-MM-DD HH:mm:ss');

    await organizationDbConnection(ehrTables.salaryImportTracking).insert({
      Import_Id: importId,
      Organization_Code: organizationCode,
      Initiated_By: initiatedBy,
      Initiated_On: now,
      Status: 'In Progress',
      Total_Records: totalRecords,
      Successful_Records: 0,
      Failed_Records: 0,
      Processing_Start_Time: now,
      Created_On: now,
      Updated_On: now
    });

    console.log(`Import tracking created for ${importId}, total records: ${totalRecords}`);
  } catch (error) {
    console.error('Error creating import tracking:', error);
    throw error;
  }
}

/**
 * Get form name for error logging
 * @param {number} formId - Form ID
 * @returns {string} - Form name
 */
function getFormNameByFormId(formId) {
  const formMap = {
    206: 'Salary Template',
    207: 'Salary Details',
    360: 'Salary Revision'
  };
  return formMap[formId] || 'Unknown Form';
}

/**
 * Finalize batch import process with Excel file generation and S3 upload
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} salaryImportId - Salary Import ID
 * @param {Array} allRecords - All salary revision records (original input)
 * @param {Array} errorRecords - Failed salary revision records
 * @param {string} orgCode - Organization code
 * @returns {Object} - Finalization result
 */
async function finalizeBatchImport(organizationDbConnection, salaryImportId, allRecords, errorRecords, orgCode) {
  try {
    console.log('=== FINALIZING BATCH IMPORT ===');
    console.log(`Salary Import ID: ${salaryImportId}`);
    console.log(`Total Records: ${allRecords.length}, Error Records: ${errorRecords.length}`);

    const endTime = new Date();
    const successCount = allRecords.length - errorRecords.length;
    const failureCount = errorRecords.length;
    const status = 'Complete'; // Always mark as complete after processing

    // Step 1: Generate Excel files
    console.log('=== GENERATING EXCEL FILES ===');
    const { generateOriginalFile, generateErrorFile, cleanupTempFile } = require('./excelFileGenerator');

    const originalFile = generateOriginalFile(allRecords, salaryImportId);
    const errorFile = generateErrorFile(errorRecords, salaryImportId);

    console.log(`Generated files: ${originalFile.fileName}, ${errorFile.fileName}`);

    // Step 2: Upload files to S3
    console.log('=== UPLOADING FILES TO S3 ===');
    const { uploadSalaryImportFiles, isS3Configured } = require('./s3FileUploader');

    let s3UploadResult = null;
    if (isS3Configured()) {
      s3UploadResult = await uploadSalaryImportFiles(originalFile, errorFile, orgCode);
      console.log('Files uploaded to S3 successfully');
    } else {
      console.warn('S3 not configured, skipping file upload');
    }

    // Step 3: Get processing start time for duration calculation
    const importRecord = await organizationDbConnection('salary_import')
      .where('Salary_Import_Id', salaryImportId)
      .select('Processing_Start_Time')
      .first();

    const startTime = importRecord?.Processing_Start_Time || endTime;
    const durationSeconds = Math.floor((endTime - new Date(startTime)) / 1000);

    // Step 4: Update salary_import record with final status and file names
    const updateData = {
      Import_Status: status,
      Success_Records: successCount,
      Failed_Records: failureCount,
      Processing_End_Time: endTime,
      Duration_Seconds: durationSeconds,
      Updated_On: endTime,
      Actual_File_Name: originalFile.fileName,
      Error_File_Name: errorFile.fileName
    };

    await organizationDbConnection('salary_import')
      .where('Salary_Import_Id', salaryImportId)
      .update(updateData);

    // Step 5: Clean up temporary files
    cleanupTempFile(originalFile.filePath);
    cleanupTempFile(errorFile.filePath);

    console.log(`Batch import ${salaryImportId} finalized with status: ${status}`);

    return {
      success: true,
      salaryImportId: salaryImportId,
      status: status,
      totalRecords: allRecords.length,
      successCount: successCount,
      failureCount: failureCount,
      durationSeconds: durationSeconds,
      files: {
        originalFile: originalFile.fileName,
        errorFile: errorFile.fileName,
        s3Upload: s3UploadResult
      },
      message: `Batch import completed. ${successCount} successful, ${failureCount} failed.`
    };

  } catch (error) {
    console.error('Error in finalizeBatchImport:', error);
    return {
      success: false,
      errorCode: 'FINALIZATION_ERROR',
      message: error.message || 'An error occurred during batch import finalization'
    };
  }
}

/**
 * Sanitize input for logging (remove sensitive data if needed)
 * @param {Object} input - Input object to sanitize
 * @returns {Object} - Sanitized copy of input
 */
function sanitizeInputForLogging(input) {
  if (!input) return null;

  // Create shallow copy and remove any sensitive nested objects if needed
  const copy = { ...input };

  // Keep the essential info for debugging
  return copy;
}

module.exports = {
  generateImportId,
  logImportError,
  updateImportTracking,
  createImportTracking,
  finalizeBatchImport,
  getFormNameByFormId,
  sanitizeInputForLogging
};
