/**
 * S3 file upload utilities for salary import
 */

const AWS = require('aws-sdk');

// Initialize S3 client
const s3 = new AWS.S3({
  region: process.env.AWS_REGION || 'us-east-1'
});

/**
 * Upload file to S3 bucket
 * @param {Buffer} fileBuffer - File buffer to upload
 * @param {string} fileName - Name of the file
 * @param {string} orgCode - Organization code
 * @param {string} contentType - MIME type of file
 * @returns {Promise<Object>} - Upload result with S3 URL
 */
async function uploadFileToS3(fileBuffer, fileName, orgCode, contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
  try {
    // Construct S3 key path
    const s3Key = `${process.env.domainName}/${orgCode}/salary-import/${fileName}`;
    
    const uploadParams = {
      Bucket: process.env.documentsBucket,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: contentType,
      ServerSideEncryption: 'AES256',
      Metadata: {
        'uploaded-by': 'salary-import-system',
        'upload-timestamp': new Date().toISOString(),
        'organization-code': orgCode
      }
    };

    console.log(`Uploading file to S3: ${s3Key}`);
    
    const result = await s3.upload(uploadParams).promise();
    
    console.log(`File uploaded successfully to S3: ${result.Location}`);
    
    return {
      success: true,
      s3Url: result.Location,
      s3Key: s3Key,
      bucket: process.env.documentsBucket,
      fileName: fileName
    };

  } catch (error) {
    console.error('Error uploading file to S3:', error);
    throw error;
  }
}

/**
 * Upload both original and error files to S3
 * @param {Object} originalFile - Original file data { fileName, buffer }
 * @param {Object} errorFile - Error file data { fileName, buffer }
 * @param {string} orgCode - Organization code
 * @returns {Promise<Object>} - Upload results for both files
 */
async function uploadSalaryImportFiles(originalFile, errorFile, orgCode) {
  try {
    console.log(`=== UPLOADING SALARY IMPORT FILES TO S3 ===`);
    console.log(`Organization: ${orgCode}`);
    console.log(`Original file: ${originalFile.fileName}`);
    console.log(`Error file: ${errorFile.fileName}`);

    // Upload both files in parallel
    const [originalUpload, errorUpload] = await Promise.all([
      uploadFileToS3(originalFile.buffer, originalFile.fileName, orgCode),
      uploadFileToS3(errorFile.buffer, errorFile.fileName, orgCode)
    ]);

    const result = {
      success: true,
      originalFile: {
        fileName: originalFile.fileName,
        s3Url: originalUpload.s3Url,
        s3Key: originalUpload.s3Key
      },
      errorFile: {
        fileName: errorFile.fileName,
        s3Url: errorUpload.s3Url,
        s3Key: errorUpload.s3Key
      },
      bucket: process.env.documentsBucket,
      folderPath: `${process.env.domainName}/${orgCode}/salary-import/`
    };

    console.log(`Both files uploaded successfully to S3`);
    console.log(`Original file URL: ${originalUpload.s3Url}`);
    console.log(`Error file URL: ${errorUpload.s3Url}`);

    return result;

  } catch (error) {
    console.error('Error uploading salary import files to S3:', error);
    throw error;
  }
}

/**
 * Generate S3 file path for salary import
 * @param {string} orgCode - Organization code
 * @param {string} fileName - File name
 * @returns {string} - Complete S3 path
 */
function generateS3FilePath(orgCode, fileName) {
  return `${process.env.domainName}/${orgCode}/salary-import/${fileName}`;
}

/**
 * Check if S3 bucket and credentials are configured
 * @returns {boolean} - True if S3 is properly configured
 */
function isS3Configured() {
  return !!(process.env.documentsBucket && process.env.domainName && process.env.AWS_REGION);
}

module.exports = {
  uploadFileToS3,
  uploadSalaryImportFiles,
  generateS3FilePath,
  isS3Configured
};
